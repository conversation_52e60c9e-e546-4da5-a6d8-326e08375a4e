baseURL = "http://localhost"
title = "GitLab Development Kit"
theme = "hextra"
contentDir = "../doc"

pluralizeListTitles = false

# Geekdoc required configuration
pygmentsUseClasses = true
pygmentsCodeFences = true
disablePathToLower = true

# Required if you want to render robots.txt template
enableRobotsTXT = true
enableGitInfo = true

[params]
displayUpdatedDate = true
[params.navbar.logo]
path = "favicon.ico"
dark = "favicon.ico"
[params.search]
enable = true
type = "flexsearch"
[params.search.flexsearch]
index = "content"
[params.editURL]
enable = true
base = "https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc"
baseCommunity = "https://gitlab.com/gitlab-community/gitlab-org/gitlab-development-kit/-/blob/main/doc"

[[menu.main]]
name = "Install"
pageRef = "/"
weight = 1
[[menu.main]]
name = "Getting started"
pageRef = "/howto"
weight = 2
[[menu.main]]
name = "Troubleshooting"
pageRef = "/troubleshooting"
weight = 3
[[menu.main]]
name = "Search"
weight = 4
[menu.main.params]
type = "search"
[[menu.main]]
name = "Source"
weight = 5
url = "https://gitlab.com/gitlab-org/gitlab-development-kit/"
[menu.main.params]
icon = "gitlab"

# Needed for mermaid shortcodes
[markup]
defaultMarkdownHandler = 'goldmark'
[markup.goldmark.renderer]
# Needed for mermaid shortcode or when nesting shortcodes (e.g. img within
# columns or tabs)
unsafe = true
[markup.tableOfContents]
startLevel = 1
endLevel = 9
[markup.goldmark.parser]
autoHeadingID = true
autoHeadingIDType = "github"


[taxonomies]
tag = "tags"
