{{/* Adapted from https://github.com/imfing/hextra/blob/main/layouts/_partials/toc.html (thanks!) */}}
{{/* See license (MIT): https://github.com/imfing/hextra/blob/main/LICENSE */}}

{{/* Table of Contents */}}
{{/* TODO: toc bottom part should be able to hide */}}
{{- $toc := .Params.toc | default true -}}
{{- $onThisPage := (T "onThisPage") | default "On this page"}}
{{- $editThisPage := "Edit this page →" -}}
{{- $editThisPageCommunity := "Edit this page (community fork) →" -}}
{{- $backToTop := (T "backToTop") | default "Scroll to top" -}}

<nav class="hextra-toc hx:order-last hx:hidden hx:w-64 hx:shrink-0 hx:xl:block hx:print:hidden hx:px-4" aria-label="table of contents">
  {{- if $toc }}
    <div class="hextra-scrollbar hx:sticky hx:top-16 hx:overflow-y-auto hx:pr-4 hx:pt-6 hx:text-sm [hyphens:auto] hx:max-h-[calc(100vh-var(--navbar-height)-env(safe-area-inset-bottom))] hx:ltr:-mr-4 hx:rtl:-ml-4">
      {{- with .Fragments.Headings -}}
        <p class="hx:mb-4 hx:font-semibold hx:tracking-tight">{{ $onThisPage }}</p>
        {{- range . -}}
          <ul>
            {{- with .Headings -}}{{ template "toc-subheading" (dict "headings" . "level" 0) }}{{- end -}}
          </ul>
        {{- end -}}
      {{- end -}}

      {{- $borderClass := "hx:mt-8 hx:border-t hx:bg-white hx:pt-8 hx:shadow-[0_-12px_16px_white] hx:dark:bg-dark hx:dark:shadow-[0_-12px_16px_#111]" -}}
      {{- if not .Fragments.Headings -}}
        {{- $borderClass = "" -}}
      {{- end -}}

      {{/* TOC bottom part */}}
      <div class="{{ $borderClass }} hx:sticky hx:bottom-0 hx:flex hx:flex-col hx:items-start hx:gap-2 hx:pb-8 hx:border-gray-200 hx:dark:border-neutral-800 hx:contrast-more:border-t hx:contrast-more:border-neutral-400 hx:contrast-more:shadow-none hx:contrast-more:dark:border-neutral-400">
        {{- if site.Params.editURL.enable -}}
          {{- $editURL := site.Params.editURL.base | default "" -}}
          {{- $editURLCommunity := "" -}}
          {{- with .Params.editURL -}}
            {{/* if `editURL` is set in the front matter */}}
            {{- $editURL = . -}}
          {{- else -}}
            {{- with .File -}}
              {{- $editURL = urls.JoinPath $editURL .Path -}}
              {{- $editURLCommunity = urls.JoinPath site.Params.editURL.baseCommunity .Path -}}
            {{- end -}}
          {{- end -}}
          <a class="hx:text-xs hx:font-medium hx:text-gray-500 hx:hover:text-gray-900 hx:dark:text-gray-400 hx:dark:hover:text-gray-100 hx:contrast-more:text-gray-800 hx:contrast-more:dark:text-gray-50" href="{{ $editURL }}" target="_blank" rel="noreferrer">{{ $editThisPage }}</a>
          {{- if $editURLCommunity -}}
          <a class="hx:text-xs hx:font-medium hx:text-gray-500 hx:hover:text-gray-900 hx:dark:text-gray-400 hx:dark:hover:text-gray-100 hx:contrast-more:text-gray-800 hx:contrast-more:dark:text-gray-50" href="{{ $editURLCommunity }}" target="_blank" rel="noreferrer">{{ $editThisPageCommunity }}</a>
          {{- end -}}
        {{- end -}}
        {{/* Scroll To Top */}}
        <button aria-hidden="true" id="backToTop" onClick="scrollUp();" class="hx:cursor-pointer hx:transition-all hx:duration-75 hx:opacity-0 hx:text-xs hx:font-medium hx:text-gray-500 hx:hover:text-gray-900 hx:dark:text-gray-400 hx:dark:hover:text-gray-100 hx:contrast-more:text-gray-800 hx:contrast-more:dark:text-gray-50">
          <span>
            {{- $backToTop -}}
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="hx:inline hx:ltr:ml-1 hx:rtl:mr-1 hx:h-3.5 hx:w-3.5 hx:rounded-full hx:border hx:border-gray-500 hx:hover:border-gray-900 hx:dark:border-gray-400 hx:dark:hover:border-gray-100 hx:contrast-more:border-gray-800 hx:contrast-more:dark:border-gray-50">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 15.75l7.5-7.5 7.5 7.5" />
          </svg>
        </button>
      </div>
    </div>
  {{ end -}}
</nav>

{{/* TOC subheadings component. This is a recursive component that renders a list of headings. */}}
{{- define "toc-subheading" -}}
  {{- $headings := .headings -}}
  {{- $level := .level -}}
  {{- if ge $level 6 -}}
    {{ return }}
  {{- end -}}

  {{- $padding := (mul $level 4) -}}
  {{- $class := cond (eq $level 0) "hx:font-semibold" (printf "hx:ltr:pl-%d hx:rtl:pr-%d" $padding $padding) -}}

  {{- range $headings }}
    {{- if .Title }}
      <li class="hx:my-2 hx:scroll-my-6 hx:scroll-py-6">
        <a class="{{ $class }} hx:inline-block hx:text-gray-500 hx:hover:text-gray-900 hx:dark:text-gray-400 hx:dark:hover:text-gray-300 hx:contrast-more:text-gray-900 hx:contrast-more:underline hx:contrast-more:dark:text-gray-50 hx:w-full hx:break-words" href="#{{ anchorize .ID }}">
          {{- .Title | safeHTML | plainify | htmlUnescape }}
        </a>
      </li>
    {{- end -}}
    {{- with .Headings -}}
      {{ template "toc-subheading" (dict "headings" . "level" (add $level 1)) }}
    {{- end -}}

  {{- end -}}
{{- end -}}
