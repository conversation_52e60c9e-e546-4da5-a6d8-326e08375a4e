{{/* Adapted from https://github.com/imfing/hextra/blob/main/layouts/_markup/render-link.html (thanks!) */}}
{{/* See license (MIT): https://github.com/imfing/hextra/blob/main/LICENSE */}}

{{- $dest := .Destination -}}
{{- $url := urls.Parse $dest -}}

{{- if and $dest (hasPrefix $dest "/") -}}
  {{- with or (.PageInner.GetPage $url.Path) (.PageInner.Resources.Get $url.Path) (resources.Get $url.Path) -}}
    {{- $query := cond $url.RawQuery (printf "?%s" $url.RawQuery) "" -}}
    {{- $fragment := cond $url.Fragment (printf "#%s" $url.Fragment) "" -}}
    {{- $dest = printf "%s%s%s" .RelPermalink $query $fragment -}}
  {{- else -}}
    {{- $dest = (relURL (strings.TrimPrefix "/" $dest)) -}}
  {{- end -}}
{{- else if and (not (hasPrefix $dest "/")) (not (hasPrefix $dest "http")) (not (hasSuffix (index (split $dest "#") 0) "README.md")) -}}
  {{- $dest = (relref .Page (strings.TrimPrefix "/" $dest)) -}}
{{- end -}}

{{- with . -}}
  <a href="{{ $dest | safeURL }}" {{ with .Title }}title="{{ . }}"{{ end }}{{ if strings.HasPrefix .Destination "http" }}target="_blank" rel="noopener"{{ end }}>{{ .Text | safeHTML }}</a>
{{- end -}}
