---
name: gitlab_base.HeadingLink
description: |
  Do not include links in a heading.
  Headings already have self-referencing anchor links,
  and they're used for generating the table of contents.
  Adding a link will break the anchor linking behavior.
extends: existence
message: "Do not use links in headings."
vocab: false
level: error
ignorecase: true
nonword: true
link: https://docs.gitlab.com/development/documentation/styleguide/#links
scope: raw
tokens:
  - ^#+ .*\[.+\]\(\S+\).*$
