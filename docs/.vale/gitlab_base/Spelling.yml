---
name: gitlab_base.Spelling
description: |
  Checks for possible spelling mistakes in content, not code. Results from links using angle brackets (<https://example.com>) should be corrected.

  If a word is flagged as a spelling mistake incorrectly, such as a
  product name, you can submit an MR to update `spelling-exceptions.txt` with
  the missing word. Commands, like `git clone` must use backticks, and must not
  be added to the exceptions.
extends: spelling
message: "Check the spelling of '%s'. If the spelling is correct, ask a Technical Writer to add this word to the spelling exception list."
vocab: false
level: warning
ignore:
  - gitlab_base/spelling-exceptions.txt
