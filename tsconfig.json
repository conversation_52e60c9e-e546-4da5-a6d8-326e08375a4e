{
  "compilerOptions": {
    "module": "commonjs",
    "target": "ES2022",
    "outDir": "dist-desktop",
    // we need to include dom library for msw to work, see https://github.com/mswjs/msw/issues/408
    "lib": ["ES2022", "dom"],
    "allowJs": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strict": true,
    // isolated modules are required when using esbuild for packaging
    "isolatedModules": true,
    // TODO migrate catching to the unknown type
    "useUnknownInCatchVariables": false
  },
  "include": ["src/**/*", "test/**/*"],
  "exclude": ["test/e2e/**/*"]
}
