{"main": "extension", "icon": "assets/logo.png", "capabilities": {"virtualWorkspaces": {"supported": "limited", "description": "Only a basic read-only remote filesystem is available at the moment."}}, "extensionDependencies": ["vscode.git"], "activationEvents": ["onFileSystem:gitlab-remote"], "contributes": {"commands": [{"command": "gl.authenticate", "title": "Authenticate", "category": "GitLab"}, {"command": "gl.removeAccount", "title": "Remove Account from VS Code", "category": "GitLab"}, {"command": "gl.validateAccounts", "title": "Validate GitLab Accounts", "category": "GitLab"}, {"command": "gl.selectWorkspaceAccount", "title": "Select Account for this Workspace", "category": "GitLab"}, {"command": "gl.deselectWorkspaceAccount", "title": "GitLab: Remove Workspace Account Selection", "category": "Developer"}, {"command": "gl.showIssuesAssignedToMe", "title": "Show Issues Assigned to Me", "category": "GitLab"}, {"command": "gl.showMergeRequestsAssignedToMe", "title": "Show Merge Requests Assigned to Me", "category": "GitLab"}, {"command": "gl.openActiveFile", "title": "Open Active File on GitLab", "category": "GitLab"}, {"command": "gl.********************", "title": "Copy Link to Active File on GitLab", "category": "GitLab"}, {"command": "gl.openCurrentMergeRequest", "title": "Open Merge Request for Current Branch", "category": "GitLab"}, {"command": "gl.openCreateNewIssue", "title": "Create New Issue on Current Project", "category": "GitLab"}, {"command": "gl.openCreateNewMR", "title": "Create New Merge Request on Current Project", "category": "GitLab"}, {"command": "gl.openProjectPage", "title": "Open Current Project on GitLab", "category": "GitLab"}, {"command": "gl.createSnippetPatch", "title": "Create Snippet Patch", "category": "GitLab"}, {"command": "gl.applySnippetPatch", "title": "Apply Snippet Patch", "category": "GitLab"}, {"command": "gl.pipelineActions", "title": "Pipeline Actions - View, Create, Retry, or Cancel", "category": "GitLab"}, {"command": "gl.issueSearch", "title": "Search Project Issues (Supports Filters)", "category": "GitLab"}, {"command": "gl.mergeRequestSearch", "title": "Search Project Merge Requests (Supports Filters)", "category": "GitLab"}, {"command": "gl.advancedSearch", "title": "Advanced Search (Issues, Merge Requests, Commits, Comments...)", "category": "GitLab"}, {"command": "gl.compareCurrentBranch", "title": "Compare Current Branch with Default Branch", "category": "GitLab"}, {"command": "gl.createSnippet", "title": "Create Snippet", "category": "GitLab"}, {"command": "gl.insertSnippet", "title": "Insert Snippet", "category": "GitLab"}, {"command": "gl.validateCIConfig", "title": "Validate GitLab CI/CD Configuration", "category": "GitLab"}, {"command": "gl.showMergedCIConfig", "title": "Show Merged GitLab CI/CD Configuration", "category": "GitLab", "icon": "$(open-preview)"}, {"command": "gl.sidebarViewAsList", "title": "View Sidebar as List", "category": "GitLab", "icon": "$(list-flat)"}, {"command": "gl.sidebarViewAsTree", "title": "View Sidebar as Tree", "category": "GitLab", "icon": "$(list-tree)"}, {"command": "gl.refreshSidebar", "title": "Refresh Sidebar", "category": "GitLab", "icon": {"light": "assets/images/light/refresh.svg", "dark": "assets/images/dark/refresh.svg"}}, {"command": "gl.openMrFile", "title": "Open Changed File in Local Project", "category": "GitLab", "icon": "$(go-to-file)"}, {"command": "gl.resolve<PERSON><PERSON>ead", "title": "Resolve <PERSON>hread", "category": "GitLab", "icon": "$(pass)"}, {"command": "gl.unresolveThread", "title": "Unresolve <PERSON>", "category": "GitLab", "icon": "$(pass-filled)"}, {"command": "gl.deleteComment", "title": "Delete Comment", "category": "GitLab", "icon": "$(trash)"}, {"command": "gl.startEditingComment", "title": "Edit Comment", "category": "GitLab", "icon": "$(edit)"}, {"command": "gl.cancelEditingComment", "title": "Cancel", "category": "GitLab"}, {"command": "gl.cancelFailedComment", "title": "Cancel", "category": "GitLab"}, {"command": "gl.retryFailedComment", "title": "Add Comment Now", "category": "GitLab"}, {"command": "gl.submitCommentEdit", "title": "Save Comment", "category": "GitLab"}, {"command": "gl.createComment", "title": "Add Comment Now", "category": "GitLab"}, {"command": "gl.checkoutMrBranch", "title": "Check out Merge Request Branch"}, {"command": "gl.openInGitLab", "title": "Open in GitLab"}, {"command": "gl.copyLinkToClipboard", "title": "Copy link to clipboard"}, {"command": "gl.cloneWiki", "title": "Clone Wiki", "category": "GitLab"}, {"command": "gl.openRepository", "title": "Open Remote Repository", "category": "GitLab"}, {"command": "gl.selectProjectForRepository", "title": "Select Project for Repository", "category": "GitLab"}, {"command": "gl.selectProject", "title": "Select GitLab Project", "category": "GitLab"}, {"command": "gl.assignProject", "title": "Manually Assign GitLab Project", "category": "GitLab"}, {"command": "gl.clearSelectedProject", "title": "Clear Selected Project", "category": "GitLab", "icon": "$(close)"}, {"command": "gl.downloadArtifacts", "title": "Download Artifacts", "category": "GitLab"}, {"command": "gl.<PERSON><PERSON>ob", "title": "Execute Job", "category": "GitLab"}, {"command": "gl.retry<PERSON><PERSON>", "title": "Retry Job", "category": "GitLab"}, {"command": "gl.cancel<PERSON>ob", "title": "Cancel Job", "category": "GitLab"}, {"command": "gl.retryF<PERSON><PERSON>ipelineJobs", "title": "Retry Failed Jobs", "category": "GitLab"}, {"command": "gl.cancelPipeline", "title": "Cancel Pipeline", "category": "GitLab"}, {"command": "gl.openTraceArtifact", "title": "Display Log", "category": "GitLab"}, {"command": "gl.waitF<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Display Log", "category": "GitLab"}, {"command": "gl.saveRawJobTrace", "title": "Save Raw Job Trace", "category": "GitLab", "icon": "$(save-as)"}, {"command": "gl.scrollToBottom", "title": "Scroll to Bottom", "enablement": "gitlab.canScrollToBottom", "category": "GitLab", "icon": "$(fold-down)"}, {"command": "gl.viewSecurityFinding", "title": "View Security Finding Details", "category": "GitLab"}, {"command": "gl.restartLanguageServer", "title": "Restart GitLab Language Server", "category": "GitLab"}, {"command": "gl.runSecurityScan", "title": "Run Remote Scan (SAST)", "category": "GitLab", "enablement": "!gitlab:noAccount"}, {"command": "gl.runSecurityScanViewAction", "title": "Scan current file", "category": "GitLab", "icon": "$(file-code)"}, {"command": "gl.publishToGitLab", "title": "Publish Workspace to GitLab", "category": "GitLab"}, {"command": "gl.agenticChat.showHistoryView", "title": "Chat History", "category": "GitLab", "icon": "$(history)"}, {"command": "gl.agenticChat.startNewConversation", "title": "Add New Chat", "category": "GitLab", "icon": "$(add)"}, {"command": "gl.mcp.openUserConfig", "title": "Open User Settings (JSON)", "category": "GitLab MCP"}, {"command": "gl.knowledgeGraph.show", "title": "Show GitLab Knowledge Graph", "category": "GitLab"}], "menus": {"commandPalette": [{"command": "gl.selectWorkspaceAccount", "when": "!gitlab:noAccount"}, {"command": "gl.deselectWorkspaceAccount", "when": "!gitlab:noAccount && !gitlab:shouldSelectAccount"}, {"command": "gl.selectProjectForRepository", "when": "false"}, {"command": "gl.resolve<PERSON><PERSON>ead", "when": "false"}, {"command": "gl.unresolveThread", "when": "false"}, {"command": "gl.deleteComment", "when": "false"}, {"command": "gl.startEditingComment", "when": "false"}, {"command": "gl.cancelEditingComment", "when": "false"}, {"command": "gl.cancelFailedComment", "when": "false"}, {"command": "gl.retryFailedComment", "when": "false"}, {"command": "gl.submitCommentEdit", "when": "false"}, {"command": "gl.createComment", "when": "false"}, {"command": "gl.checkoutMrBranch", "when": "false"}, {"command": "gl.downloadArtifacts", "when": "false"}, {"command": "gl.<PERSON><PERSON>ob", "when": "false"}, {"command": "gl.retry<PERSON><PERSON>", "when": "false"}, {"command": "gl.cancel<PERSON>ob", "when": "false"}, {"command": "gl.retryF<PERSON><PERSON>ipelineJobs", "when": "false"}, {"command": "gl.cancelPipeline", "when": "false"}, {"command": "gl.openTraceArtifact", "when": "false"}, {"command": "gl.waitF<PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "false"}, {"command": "gl.saveRawJobTrace", "when": "false"}, {"command": "gl.scrollToBottom", "when": "false"}, {"command": "gl.openMrFile", "when": "false"}, {"command": "gl.openInGitLab", "when": "false"}, {"command": "gl.showIssuesAssignedToMe", "when": "gitlab:validState"}, {"command": "gl.showMergeRequestsAssignedToMe", "when": "gitlab:validState"}, {"command": "gl.openActiveFile", "when": "gitlab:validState && editorIsOpen"}, {"command": "gl.********************", "when": "gitlab:validState && editorIsOpen"}, {"command": "gl.openCurrentMergeRequest", "when": "gitlab:validState"}, {"command": "gl.openCreateNewIssue", "when": "gitlab:validState"}, {"command": "gl.openCreateNewMR", "when": "gitlab:validState"}, {"command": "gl.openProjectPage", "when": "gitlab:validState"}, {"command": "gl.pipelineActions", "when": "gitlab:validState"}, {"command": "gl.issueSearch", "when": "gitlab:validState"}, {"command": "gl.mergeRequestSearch", "when": "gitlab:validState"}, {"command": "gl.advancedSearch", "when": "gitlab:validState"}, {"command": "gl.compareCurrentBranch", "when": "gitlab:validState"}, {"command": "gl.createSnippet", "when": "gitlab:validState && editorIsOpen"}, {"command": "gl.insertSnippet", "when": "gitlab:validState && editorIsOpen"}, {"command": "gl.validateCIConfig", "when": "gitlab:validState && editorIsOpen"}, {"command": "gl.showMergedCIConfig", "when": "gitlab:validState && editorIsOpen"}, {"command": "gl.sidebarViewAsList", "when": "false"}, {"command": "gl.sidebarViewAsTree", "when": "false"}, {"command": "gl.refreshSidebar", "when": "gitlab:validState"}, {"command": "gl.cloneWiki", "when": "!gitlab:noAccount"}, {"command": "gl.createSnippetPatch", "when": "gitlab:validState"}, {"command": "gl.applySnippetPatch", "when": "gitlab:validState"}, {"command": "gl.selectProject", "when": "false"}, {"command": "gl.assignProject", "when": "false"}, {"command": "gl.clearSelectedProject", "when": "false"}, {"command": "gl.publishToGitLab", "when": "!gitlab:noAccount && workbenchState != empty"}, {"command": "gl.runSecurityScan", "when": "config.gitlab.securityScans.enabled && (activeEditor =~ /textFileEditor$/ || activeEditor =~ /textDiffEditor$/)"}, {"command": "gl.openChat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment) && activeViewlet !== workbench.view.extension.gitlab-duo"}, {"command": "gl.close<PERSON>hat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment) && activeViewlet === workbench.view.extension.gitlab-duo"}, {"command": "gl.explainSelectedCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)"}, {"command": "gl.generateTests", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)"}, {"command": "gl.refactorCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)"}, {"command": "gl.fixCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)"}, {"command": "gl.newChatConversation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)"}, {"command": "gl.webview.duoChatV2.show", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment) && activeViewlet !== workbench.view.extension.gitlab-duo"}, {"command": "gl.webview.closeChat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment) && activeViewlet === workbench.view.extension.gitlab-duo"}, {"command": "gl.webview.explainSelectedCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)"}, {"command": "gl.webview.generateTests", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)"}, {"command": "gl.webview.refactorCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)"}, {"command": "gl.webview.fixCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)"}, {"command": "gl.webview.newChatConversation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)"}, {"command": "gl.knowledgeGraph.show", "when": "gitlab:knowledgeGraphReady"}], "view/title": [{"command": "gl.sidebarViewAsList", "when": "view =~ /(currentBranchInfo|issuesAndMrs)/ && gitlab.sidebarView == tree", "group": "navigation@1"}, {"command": "gl.sidebarViewAsTree", "when": "view =~ /(currentBranchInfo|issuesAndMrs)/ && gitlab.sidebarView == list", "group": "navigation@1"}, {"command": "gl.refreshSidebar", "when": "view =~ /(currentBranchInfo|issuesAndMrs)/", "group": "navigation@2"}, {"command": "gl.runSecurityScanViewAction", "when": "view == remoteSecurityScanning", "group": "navigation"}, {"command": "gl.agenticChat.showHistoryView", "when": "false", "group": "navigation"}, {"command": "gl.agenticChat.startNewConversation", "when": "false", "group": "navigation"}], "view/item/context": [{"command": "gl.openMrFile", "when": "viewItem == changed-file-item", "group": "inline"}, {"command": "gl.checkoutMrBranch", "when": "view =~ /issuesAndMrs/ && viewItem =~ /mr-item-from-same-project/", "group": "navigation@1"}, {"command": "gl.openInGitLab", "when": "viewItem =~ /web-openable/ || (view =~ /currentBranchInfo/ && viewItem =~ /with-url/)", "group": "navigation@1"}, {"command": "gl.copyLinkToClipboard", "when": "viewItem =~ /web-openable/ || (view =~ /currentBranchInfo/ && viewItem =~ /with-url/)", "group": "navigation@1"}, {"command": "gl.assignProject", "when": "view =~ /issuesAndMrs/ && viewItem == no-project-detected"}, {"command": "gl.selectProject", "when": "view =~ /issuesAndMrs/ && viewItem == multiple-projects-detected"}, {"command": "gl.clearSelectedProject", "when": "view =~ /issuesAndMrs/ && viewItem == selected-project", "group": "inline"}, {"command": "gl.cancel<PERSON>ob", "when": "view =~ /currentBranchInfo/ && viewItem =~ /cancellable-job/"}, {"command": "gl.retry<PERSON><PERSON>", "when": "view =~ /currentBranchInfo/ && viewItem =~ /retryable-job/"}, {"command": "gl.cancelPipeline", "when": "view =~ /currentBranchInfo/ && viewItem =~ /cancellable-pipeline/"}, {"command": "gl.retryF<PERSON><PERSON>ipelineJobs", "when": "view =~ /currentBranchInfo/ && viewItem =~ /retryable-pipeline/"}, {"command": "gl.<PERSON><PERSON>ob", "when": "view =~ /currentBranchInfo/ && viewItem =~ /executable-job/"}, {"command": "gl.downloadArtifacts", "when": "view =~ /currentBranchInfo/ && viewItem =~ /with-artifacts/"}, {"command": "gl.openTraceArtifact", "when": "view =~ /currentBranchInfo/ && viewItem =~ /with-trace/", "group": "navigation@2"}, {"command": "gl.waitF<PERSON><PERSON><PERSON><PERSON><PERSON>", "when": "view =~ /currentBranchInfo/ && viewItem =~ /pending-job/", "group": "navigation@2"}], "comments/comment/title": [{"command": "gl.startEditingComment", "group": "inline@1", "when": "commentController =~ /^gitlab-mr-/ && comment =~ /canAdmin/"}, {"command": "gl.deleteComment", "group": "inline@2", "when": "commentController =~ /^gitlab-mr-/ && comment =~ /canAdmin/"}], "comments/comment/context": [{"command": "gl.submitCommentEdit", "group": "inline@1", "when": "commentController =~ /^gitlab-mr-/ && comment =~ /synced-comment/"}, {"command": "gl.cancelEditingComment", "group": "inline@2", "when": "commentController =~ /^gitlab-mr-/ && comment =~ /synced-comment/"}, {"command": "gl.retryFailedComment", "group": "inline@1", "when": "commentController =~ /^gitlab-mr-/ && comment =~ /failed-comment/"}, {"command": "gl.cancelFailedComment", "group": "inline@2", "when": "commentController =~ /^gitlab-mr-/ && comment =~ /failed-comment/"}], "comments/commentThread/title": [{"command": "gl.resolve<PERSON><PERSON>ead", "group": "inline@1", "when": "commentController =~ /^gitlab-mr-/ && commentThread == unresolved"}, {"command": "gl.unresolveThread", "group": "inline@2", "when": "commentController =~ /^gitlab-mr-/ && commentThread == resolved"}], "comments/commentThread/context": [{"command": "gl.createComment", "group": "inline", "when": "commentController =~ /^gitlab-mr-/"}], "editor/title": [{"command": "gl.openMrFile", "when": "resourceScheme == 'gl-review' && resourceFilename != '' && isInDiffEditor", "group": "navigation@-99"}, {"command": "gl.showMergedCIConfig", "when": "gitlab:validState && resourceScheme != 'gl-merged-ci-yaml' && resourceFilename == '.gitlab-ci.yml'", "group": "navigation@-99"}, {"command": "gl.saveRawJobTrace", "when": "resourceScheme == 'gl-job-log'", "group": "navigation@-99"}, {"command": "gl.scrollToBottom", "when": "resourceScheme == 'gl-job-log'", "group": "navigation"}], "gl.gitlabDuo": [{"command": "gl.explainSelectedCode", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && !gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.generateTests", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && !gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.refactorCode", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && !gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.fixCode", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && !gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.webview.explainSelectedCode", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.webview.generateTests", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.webview.refactorCode", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && gitlab.featureFlags.languageServerWebviews"}, {"command": "gl.webview.fixCode", "group": "navigation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && gitlab.featureFlags.languageServerWebviews"}]}, "viewsContainers": {"activitybar": [{"id": "gitlab-workflow", "title": "GitLab Workflow", "icon": "$(gitlab-logo)"}, {"id": "*********************", "title": "GitLab Duo Agent Platform (Beta)", "icon": "assets/icons/*********************.svg"}]}, "views": {"gitlab-workflow": [{"id": "issuesAndMrs", "name": "Issues and Merge Requests"}, {"id": "remoteSecurityScanning", "name": "GitLab Remote Scan (SAST)", "when": "gitlab.featureFlags.remoteSecurityScans && !gitlab:noAccount"}, {"id": "currentBranchInfo", "name": "For current branch", "when": "gitlab:validState"}], "gitlab-duo": [{"type": "webview", "id": "gl.chat<PERSON>iew", "name": "", "when": "(!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment) && config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject"}, {"type": "webview", "id": "gl.webview.duo-chat-v2", "name": "", "when": "(gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment) && config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject"}], "*********************": [{"type": "webview", "id": "gl.webview.agentic-tabs", "name": "", "when": "config.gitlab.duoAgentPlatform.enabled && (gitlab.featureFlags.duo_agentic_chat || gitlab.featureFlags.duo_workflow)"}]}, "viewsWelcome": [{"view": "issuesAndMrs", "contents": "Welcome to the GitLab Workflow extension! \n [Authenticate to GitLab instance](command:gl.authenticate)\nTo learn more, read [the setup information](https://gitlab.com/gitlab-org/gitlab-vscode-extension#setup) for this extension.", "when": "gitlab:noAccount"}, {"view": "issuesAndMrs", "contents": "No Git repository available. To learn how to fix this, check the Source Control tab.\n[Open Source Control](command:workbench.view.scm)", "when": "!gitlab:openRepositoryCount"}, {"view": "issuesAndMrs", "contents": "Multiple GitLab accounts found for this workspace.\n[Select an account](command:gl.selectWorkspaceAccount)", "when": "gitlab:shouldSelectAccount"}, {"view": "scm", "contents": "You can push the current folder to a new GitLab project.\n[$(gitlab-logo) Publish to GitLab](command:gl.publishToGitLab)", "when": "config.git.enabled && git.state == initialized && !gitlab:noAccount && workbenchState == folder"}, {"view": "scm", "contents": "You can push any of the current folders to a new GitLab project.\n[$(gitlab-logo) Publish to GitLab](command:gl.publishToGitLab)", "when": "config.git.enabled && git.state == initialized && !gitlab:noAccount && workbenchState == workspace"}], "configuration": [{"id": "duo", "properties": {"gitlab.duoCodeSuggestions.openTabsContext": {"type": "boolean", "default": true, "order": 100, "description": "Use the contents of open tabs as context"}, "gitlab.duoAgentPlatform.enabled": {"markdownDescription": "Enable GitLab Duo Agent Platform\n\n_This feature is in beta._", "type": "boolean", "order": 3, "default": true, "tags": ["beta"]}, "gitlab.duoAgentPlatform.connectionType": {"type": "string", "enum": ["grpc", "websocket"], "enumItemLabels": ["gRPC", "WebSocket"], "default": "grpc", "order": 4}, "gitlab.duoAgentPlatform.defaultNamespace": {"description": "The default group or namespace path for GitLab Agentic Platform when the extension can't get GitLab project details. Example: gitlab-org", "type": "string", "order": 4}}}, {"id": "custom-certificates", "order": 2, "title": "Custom Certificates", "properties": {"gitlab.ca": {"type": "string", "default": null, "order": 101, "description": "Custom CA file to use (example: /etc/ssl/certs/ca-certificates.crt)"}, "gitlab.cert": {"type": "string", "default": null, "order": 102, "description": "Custom certificate file to use (example: /etc/ssl/certs/certificate.crt)"}, "gitlab.certKey": {"type": "string", "default": null, "order": 103, "description": "Custom certificate key file to use (example: /etc/ssl/certs/certificateKey.key)"}, "gitlab.ignoreCertificateErrors": {"type": "boolean", "default": false, "description": "Ignore TLS/SSL certificate errors when calling the GitLab API"}}}, {"id": "codeSecurity", "order": 3, "title": "Code Security", "properties": {"gitlab.real-timeSecurityScan.enabled": {"type": "boolean", "default": false, "description": "Enable Real-time SAST scan", "tags": ["experimental"]}, "gitlab.real-timeSecurityScan.scanFileOnSave": {"type": "boolean", "default": true, "description": "Enable scanning on file save", "tags": ["experimental"]}}}, {"id": "other", "properties": {"gitlab.branchProtection": {"type": "boolean", "default": true, "description": "Apply branch protection rules from GitLab"}, "gitlab.showStatusBarLinks": {"type": "boolean", "default": true, "description": "Display all GitLab-related links in the status bar (requires restarting Visual Studio Code)", "deprecationMessage": "To hide an item from the status bar, right-click the item in VS Code."}, "gitlab.showIssueLinkOnStatusBar": {"type": "boolean", "default": true, "description": "Display the GitLab issue link in the status bar", "deprecationMessage": "To hide an item from the status bar, right-click the item in VS Code."}, "gitlab.showMrStatusOnStatusBar": {"type": "boolean", "default": true, "description": "Display the GitLab merge request status in the status bar", "deprecationMessage": "To hide an item from the status bar, you should right-click the item in VS Code."}, "gitlab.pipelineGitRemoteName": {"type": "string", "default": null, "description": "Name of the Git remote to use when locating the GitLab project for your pipeline. Keep empty for default"}, "gitlab.showPipelineUpdateNotifications": {"type": "boolean", "default": false, "description": "Show notification in VS Code when the pipeline status changes"}, "gitlab.customQueries": {"type": "array", "minItems": 1, "items": {"type": "object", "title": "Custom GitLab Query", "required": ["name"], "properties": {"name": {"type": "string", "description": "The label to show in the GitLab panel"}, "maxResults": {"type": "number", "description": "The maximum number of results to show", "default": 20, "maximum": 100, "minimum": 1}, "orderBy": {"type": "string", "description": "Return issues ordered by the selected value. Not applicable for vulnerabilities", "enum": ["created_at", "updated_at", "priority", "due_date", "relative_position", "label_priority", "milestone_due", "popularity", "weight"], "default": "created_at"}, "sort": {"type": "string", "description": "Return issues sorted in ascending or descending order. Not applicable for vulnerabilities", "enum": ["asc", "desc"], "default": " desc"}, "scope": {"type": "string", "description": "Return GitLab items for the given scope. Not applicable for epics. \"assigned_to_me\" and \"created_by_me\" are not applicable for vulnerabilities. \"dismissed\" is not applicable for issues and merge requests", "enum": ["assigned_to_me", "created_by_me", "dismissed", "all"], "default": "all"}, "type": {"type": "string", "description": "The type of GitLab items to return. If 'snippets' is selected, no other filters work. Epics available only on GitLab Ultimate or GitLab Gold.", "enum": ["issues", "merge_requests", "epics", "snippets", "vulnerabilities"], "default": "merge_requests"}, "noItemText": {"type": "string", "description": "The text to show if the query returns no items", "default": "No items found."}, "state": {"type": "string", "description": "Return \"all\" issues or just those that are \"opened\" or \"closed\". Not applicable for vulnerabilities", "enum": ["all", "opened", "closed"], "default": "opened"}, "labels": {"type": "array", "description": "Array of label names. The GitLab item must have all labels to be returned. \"None\" lists all GitLab items with no labels. \"Any\" lists all GitLab issues with at least one label. Predefined names are not case-sensitive. Not applicable for vulnerabilities", "items": {"type": "string"}}, "milestone": {"type": "string", "description": "The milestone title. \"None\" lists all GitLab items with no milestone. \"Any\" lists all GitLab items that have an assigned milestone. Not applicable for epics and vulnerabilities"}, "author": {"type": "string", "description": "Return GitLab items created by the given username. Not applicable for vulnerabilities"}, "assignee": {"type": "string", "description": "Returns GitLab items assigned to the given username. \"None\" returns unassigned GitLab items. \"Any\" returns GitLab items with an assignee. Not applicable for epics and vulnerabilities"}, "search": {"type": "string", "description": "Search GitLab items against their title and description. Not applicable for vulnerabilities"}, "searchIn": {"type": "string", "description": "Modify the scope of the search attribute. Not applicable for epics and vulnerabilities", "enum": ["all", "title", "description"], "default": "all"}, "createdAfter": {"type": "string", "format": "date", "description": "Return GitLab items created after the given date. ISO 8601 formatted, like 2016-03-11T03:45:40Z. Not applicable for vulnerabilities"}, "createdBefore": {"type": "string", "format": "date", "description": "Return GitLab items created before the given date. ISO 8601 formatted, like 2016-03-11T03:45:40Z. Not applicable for vulnerabilities"}, "updatedAfter": {"type": "string", "format": "date", "description": "Return GitLab items updated after the given date. ISO 8601 formatted, like 2016-03-11T03:45:40Z. Not applicable for vulnerabilities"}, "updatedBefore": {"type": "string", "format": "date", "description": "Return GitLab items updated before the given date. ISO 8601 formatted, like 2016-03-11T03:45:40Z. Not applicable for vulnerabilities"}, "wip": {"type": "string", "enum": ["yes", "no"], "description": "Filter merge requests against their draft status. \"yes\" to return only draft merge requests, \"no\" to return non-draft merge requests. Works only with merge requests. This parameter is an alias for \"draft\""}, "draft": {"type": "string", "enum": ["yes", "no"], "description": "Filter merge requests against their draft status. \"yes\" to return only draft merge requests, \"no\" to return non-draft merge requests. Works only with merge requests", "default": "no"}, "confidential": {"type": "boolean", "description": "Filter confidential or public issues. Works only with issues", "default": false}, "excludeLabels": {"type": "array", "description": "Array of label names the GitLab item must not have to be returned. Predefined names are not case-sensitive. Works only with issues", "items": {"type": "string"}}, "excludeMilestone": {"type": "string", "description": "The milestone title to exclude. Works only with issues"}, "excludeAuthor": {"type": "string", "description": "Return GitLab items not created by the given username. Works only with issues"}, "excludeAssignee": {"type": "string", "description": "Returns GitLab items not assigned to the given username. Works only with issues"}, "excludeSearch": {"type": "string", "description": "Search GitLab items without the search key in their title or description. Works only with issues"}, "excludeSearchIn": {"type": "string", "description": "Modify the scope of the excludeSearch attribute. Works only with issues", "enum": ["all", "title", "description"], "default": "all"}, "reportTypes": {"type": "array", "description": "Returns vulnerabilities belonging to specified report types. Works only with vulnerabilities", "items": {"type": "string", "enum": ["sast", "dast", "dependency_scanning", "container_scanning"]}}, "severityLevels": {"type": "array", "description": "Returns vulnerabilities belonging to specified severity levels. Defaults to 'all'. Works only with vulnerabilities", "items": {"type": "string", "enum": ["undefined", "info", "unknown", "low", "medium", "high", "critical"]}}, "confidenceLevels": {"type": "array", "description": "Returns vulnerabilities belonging to specified confidence levels. Defaults to 'all'. Works only with vulnerabilities", "items": {"type": "string", "enum": ["undefined", "ignore", "unknown", "experimental", "low", "medium", "high", "confirmed"]}}, "reviewer": {"type": "string", "description": "Returns GitLab merge requests assigned for review to the given username. When set to \"<current_user>\", the current user's username is used."}}}, "default": [{"name": "Issues assigned to me", "type": "issues", "scope": "assigned_to_me", "state": "opened", "noItemText": "No issues assigned to you."}, {"name": "Issues created by me", "type": "issues", "scope": "created_by_me", "state": "opened", "noItemText": "No issues created by you."}, {"name": "Merge requests assigned to me", "type": "merge_requests", "scope": "assigned_to_me", "state": "opened", "noItemText": "No merge requests assigned to you."}, {"name": "<PERSON><PERSON> requests I'm reviewing", "type": "merge_requests", "reviewer": "<current_user>", "state": "opened", "noItemText": "No merge requests for you to review."}, {"name": "Merge requests created by me", "type": "merge_requests", "scope": "created_by_me", "state": "opened", "noItemText": "No merge requests created by you."}, {"name": "All project merge requests", "type": "merge_requests", "scope": "all", "state": "opened", "noItemText": "The project has no merge requests"}], "description": "Custom views in the GitLab panel"}}}, {"id": "distributed-binaries", "order": 4, "title": "GitLab Distributed Binaries", "properties": {"gitlab.distributedBinaries.skipIntegrityChecks": {"type": "boolean", "default": false, "description": "Skip integrity checks"}}}], "walkthroughs": [{"id": "placeholder", "title": "How to use GitLab Workflow", "description": "**Your workflow, your way.** View issues, create merge requests, and access GitLab Duo AI features from your IDE.", "steps": [{"id": "authenticate-with-gitlab", "title": "Authenticate with GitLab", "description": "For GitLab.com and GitLab Dedicated, [sign in](command:gl.authenticate) with your GitLab account.\n\nFor GitLab Self-Managed, [generate a personal access token, then sign in](command:gl.authenticate) with it.\n\nHaving installation issues? Review the [setup instructions](https://gitlab.com/gitlab-org/gitlab-vscode-extension#setup). If you can't access paid features, contact your administrator.", "media": {"markdown": "walkthroughs/welcome/authenticate_with_gitlab.md"}}, {"id": "accept-duo-code-suggestion", "title": "Accept a GitLab Duo Code Suggestion", "description": "To receive Code Suggestions generated by AI, open a [supported file type](https://docs.gitlab.com/user/project/repository/code_suggestions/supported_extensions/#supported-languages-by-ide) and start writing code. Press ``Tab`` to accept a suggestion, ``Escape`` to cancel, or continue typing to ignore.", "media": {"markdown": "walkthroughs/welcome/accept_duo_code_suggestion.md"}}, {"id": "generate-duo-code-suggestion", "title": "Generate a Code Suggestion", "description": "While writing a comment, give an instruction in natural language, then press ``Enter``.\n\nGitLab Duo generates a Code Suggestion based on the context of your comment and the rest of your code.", "media": {"markdown": "walkthroughs/welcome/generate_duo_code_suggestion.md"}}, {"id": "ask-duo-chat-a-question", "title": "Ask <PERSON> a question about code", "description": "Highlight some code, and ask <PERSON> a question to get contextual answers.\nWrite tests, refactor, or explain highlighted code. Right-click or enter ``/`` in <PERSON> Cha<PERSON> to use slash commands.\n[Open Duo Chat](command:gl.openChat)", "media": {"markdown": "walkthroughs/welcome/ask_duo_chat_a_question.md"}}, {"id": "duo-tutorial", "title": "Learn with the Duo Tutorial", "description": "Boost your productivity with interactive exercises that teach advanced Duo techniques.\n Run the [GitLab: Duo Tutorial](command:gl.duoTutorial) command to start.", "media": {"image": "walkthroughs/welcome/duo-tutorial.png", "altText": "Duo Tutorial"}, "completionEvents": ["onCommand:gl.duoTutorial"]}, {"id": "review-assigned-issues", "title": "Review your assigned issues", "description": "GitLab Workflow is separate from Duo Chat in the activity bar. Open it and expand [**Issues and Merge Requests**](command:issuesAndMrs.focus) to view your assigned issues, and leave comments without leaving VS Code.", "media": {"markdown": "walkthroughs/welcome/review_assigned_issues.md"}}, {"id": "create-merge-request", "title": "Create a merge request", "description": "When you're on a feature branch, use [**Create MR**](command:gl.openCreateNewMR), in the status bar at the bottom of VS Code to start a merge request in the GitLab UI.", "media": {"markdown": "walkthroughs/welcome/create_merge_request.md"}}]}], "resourceLabelFormatters": [{"scheme": "gitlab-remote", "authority": "*", "formatting": {"label": "${path}", "stripPathStartingSeparator": true, "separator": "/", "workspaceSuffix": "GitLab"}}], "keybindings": [{"command": "gl.openChat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment) && activeViewlet !== workbench.view.extension.gitlab-duo", "key": "alt+d"}, {"command": "gl.close<PERSON>hat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment) && activeViewlet === workbench.view.extension.gitlab-duo && gitlab:chatFocused", "key": "alt+d"}, {"command": "gl.focusChat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment) && activeViewlet === workbench.view.extension.gitlab-duo && !gitlab:chatFocused", "key": "alt+d"}, {"command": "gl.explainSelectedCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)", "key": "alt+e"}, {"command": "gl.generateTests", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)", "key": "alt+t"}, {"command": "gl.refactorCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)", "key": "alt+r"}, {"command": "gl.newChatConversation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (!gitlab.featureFlags.languageServerWebviews || gitlab:isRemoteEnvironment)", "key": "alt+n"}, {"command": "gl.webview.duoChatV2.show", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment) && activeViewlet !== workbench.view.extension.gitlab-duo", "key": "alt+d"}, {"command": "gl.webview.closeChat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment) && activeViewlet === workbench.view.extension.gitlab-duo && gitlab:chatFocused", "key": "alt+d"}, {"command": "gl.webview.focusChat", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment) && activeViewlet === workbench.view.extension.gitlab-duo && !gitlab:chatFocused", "key": "alt+d"}, {"command": "gl.webview.explainSelectedCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)", "key": "alt+e"}, {"command": "gl.webview.generateTests", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)", "key": "alt+t"}, {"command": "gl.webview.refactorCode", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && editorHasSelection && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)", "key": "alt+r"}, {"command": "gl.webview.newChatConversation", "when": "config.gitlab.duoChat.enabled && gitlab:chatAvailable && gitlab:chatAvailableForProject && (gitlab.featureFlags.languageServerWebviews && !gitlab:isRemoteEnvironment)", "key": "alt+n"}], "icons": {"gitlab-code-suggestions-loading": {"description": "GitLab Code Suggestions Loading", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA01"}}, "gitlab-code-suggestions-enabled": {"description": "GitLab Code Suggestions Enabled", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA02"}}, "gitlab-code-suggestions-disabled": {"description": "GitLab Code Suggestions Disabled", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA03"}}, "gitlab-code-suggestions-error": {"description": "GitLab Code Suggestions Error", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA04"}}, "gitlab-logo": {"description": "GitLab", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA05"}}, "gitlab-duo-chat-enabled": {"description": "GitLab Duo Chat Enabled", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA06"}}, "gitlab-duo-chat-disabled": {"description": "GitLab Duo Chat Disabled", "default": {"fontPath": "./assets/gitlab_icons.woff", "fontCharacter": "\\eA07"}}}}}