[2m2025-09-23T09:22:50.525056Z[0m [[32m[1minfo     [0m] [1mApplication shutdown complete.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:22:50.525120Z[0m [[32m[1minfo     [0m] [1mFinished server process [66436][0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:23:13.824795Z[0m [[32m[1minfo     [0m] [1mMetrics HTTP server running on http://0.0.0.0:8082[0m [[0m[1m[34mmain[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:23:13.839995Z[0m [[32m[1minfo     [0m] [1mStarted server process [77284][0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:23:13.840093Z[0m [[32m[1minfo     [0m] [1mWaiting for application startup.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:23:13.868089Z[0m [[32m[1minfo     [0m] [1mApplication startup complete. [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:23:44.912807Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:62182 - "HEAD /monitoring/healthz HTTP/1.1" 405[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m62182[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m99451d9760da485391b4e42fd120b965[0m [36mcpu_s[0m=[35m0.00011300000000025179[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.00011279102182015777[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mfirst_chunk_duration_s[0m=[35m0.00010083301458507776[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_exception_details[0m=[35m'405: Method Not Allowed'[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mHEAD[0m [36mpath[0m=[35m/monitoring/healthz[0m [36mrequest_arrived_at[0m=[35m2025-09-23T09:23:44.912638+00:00[0m [36mresponse_start_duration_s[0m=[35m8.212501415982842e-05[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m405[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://localhost:5052/monitoring/healthz[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-23T09:25:36.761224Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:62410 - "HEAD /monitoring/healthz HTTP/1.1" 405[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m62410[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[***********************************[0m [36mcpu_s[0m=[35m9.099999999984121e-05[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m9.179202606901526e-05[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mfirst_chunk_duration_s[0m=[35m8.233397966250777e-05[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_exception_details[0m=[35m'405: Method Not Allowed'[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mHEAD[0m [36mpath[0m=[35m/monitoring/healthz[0m [36mrequest_arrived_at[0m=[35m2025-09-23T09:25:36.761098+00:00[0m [36mresponse_start_duration_s[0m=[35m6.533402483910322e-05[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m405[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://localhost:5052/monitoring/healthz[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-23T09:25:43.857745Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:62436 - "HEAD /monitoring/healthz HTTP/1.1" 405[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m62436[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m4ca5ed8f33a847ae8e27ddd056844107[0m [36mcpu_s[0m=[35m7.899999999994023e-05[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m7.975002517923713e-05[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mfirst_chunk_duration_s[0m=[35m7.050001295283437e-05[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_exception_details[0m=[35m'405: Method Not Allowed'[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mHEAD[0m [36mpath[0m=[35m/monitoring/healthz[0m [36mrequest_arrived_at[0m=[35m2025-09-23T09:25:43.857637+00:00[0m [36mresponse_start_duration_s[0m=[35m5.76250022277236e-05[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m405[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://localhost:5052/monitoring/healthz[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-23T09:27:58.192269Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:62678 - "HEAD /monitoring/healthz HTTP/1.1" 405[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m62678[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m40ac74c4dd5a4896b9c006d2e878bccb[0m [36mcpu_s[0m=[35m7.899999999994023e-05[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m7.908296538516879e-05[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mfirst_chunk_duration_s[0m=[35m7.0915964897722e-05[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_exception_details[0m=[35m'405: Method Not Allowed'[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mHEAD[0m [36mpath[0m=[35m/monitoring/healthz[0m [36mrequest_arrived_at[0m=[35m2025-09-23T09:27:58.192162+00:00[0m [36mresponse_start_duration_s[0m=[35m5.783297820016742e-05[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m405[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://localhost:5052/monitoring/healthz[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-23T09:41:40.989525Z[0m [[32m[1minfo     [0m] [1mShutting down                 [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:41.090977Z[0m [[32m[1minfo     [0m] [1mWaiting for application shutdown.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:41.091226Z[0m [[32m[1minfo     [0m] [1mApplication shutdown complete.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:41.091285Z[0m [[32m[1minfo     [0m] [1mFinished server process [77284][0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:59.068340Z[0m [[32m[1minfo     [0m] [1mMetrics HTTP server running on http://0.0.0.0:8082[0m [[0m[1m[34mmain[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:59.086254Z[0m [[32m[1minfo     [0m] [1mStarted server process [48201][0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:59.086866Z[0m [[32m[1minfo     [0m] [1mWaiting for application startup.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-23T09:41:59.112137Z[0m [[32m[1minfo     [0m] [1mApplication startup complete. [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:15.997982Z[0m [[32m[1minfo     [0m] [1mShutting down                 [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:16.099530Z[0m [[32m[1minfo     [0m] [1mWaiting for application shutdown.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:16.100214Z[0m [[32m[1minfo     [0m] [1mApplication shutdown complete.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:16.100307Z[0m [[32m[1minfo     [0m] [1mFinished server process [48201][0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:31.909773Z[0m [[32m[1minfo     [0m] [1mMetrics HTTP server running on http://0.0.0.0:8082[0m [[0m[1m[34mmain[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mml[2m2025[2m2025-09-24T09:02:33.043244Z[0m [[32m[1minfo     [0m] [1mStarted server process [6179] [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:33.043354Z[0m [[32m[1minfo     [0m] [1mWaiting for application startup.[0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:33.066979Z[0m [[32m[1minfo     [0m] [1mApplication startup complete. [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:10.618429Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35meb1b3bffacce4a8baa66d652f40ad0cd[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:10.620151Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:59852 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0[2m2025-09-24T09:05:10.620151Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:59852 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m59852[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35meb1b3bffacce4a8baa66d652f40ad0cd[0m [36mcpu_s[0m=[35m0.002114999999999867[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.004393625014927238[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.004378250043373555[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:10.615707+00:00[0m [36mresponse_start_duration_s[0m=[35m0.004357917001470923[0[2m2025-09-24T09:05:43.906195Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35m5422a775f6a041f8a6b8133d5c695fb0[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:43.906587Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60009 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0[2m2025-09-24T09:05:43.906587Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60009 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m60009[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m5422a775f6a041f8a6b8133d5c695fb0[0m [36mcpu_s[0m=[35m0.00042500000000034177[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.0004303749883547425[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.0004190409672446549[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:43.906120+00:00[0m [36mresponse_start_duration_s[0m=[35m0.0004024579538963735[0[2m2025-09-24T09:05:55.705180Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35m85776b45cb084ad8bd6ba3da9ee6a3c0[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:55.705536Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60057 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0[2m2025-09-24T09:05:55.705536Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60057 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m60057[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m85776b45cb084ad8bd6ba3da9ee6a3c0[0m [36mcpu_s[0m=[35m0.00040400000000007097[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.000402832985855639[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.00039195799035951495[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:55.705107+00:00[0m [36mresponse_start_duration_s[0m=[35m0.00037525000516325235[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m422[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://127.0.0.1:5052/v1/chat/completions[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
8250043373555[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:10.615707+00:00[0m [36mresponse_start_duration_s[0m=[35m0.004357917001470923[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m422[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://127.0.0.1:5052/v1/chat/completions[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-24T09:05:43.906195Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35m5422a775f6a041f8a6b8133d5c695fb0[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:43.906587Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60009 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[2m2025-09-24T09:05:43.906587Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60009 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m60009[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m5422a775f6a041f8a6b8133d5c695fb0[0m [36mcpu_s[0m=[35m0.00042500000000034177[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.0004303749883547425[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.0004190409672446549[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:43.906120+00:00[0m [36mresponse_start_duration_s[0m=[35m0.0004024579538963735[0m [2m2025-09-24T09:05:55.705180Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35m85776b45cb084ad8bd6ba3[2m2025-09-24T09:05:55.705180Z[0m [[31m[1mcritical [0m] [1mAuth is [2m2025-09-24T09:05:55.705536Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60057 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[2m2025-09-24T09:05:55.705536Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60057 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m60057[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m85776b45cb084ad8bd6ba3da9ee6a3c0[0m [36mcpu_s[0m=[35m0.00040400000000007097[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.000402832985855639[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.00039195799035951495[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:55.705107+00:00[0m [36mresponse_start_duration_s[0m=[35m0.00037525000516325235[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m422[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://127.0.0.1:5052/v1/chat/completions[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
 [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:02:33.066979Z[0m [[32m[1minfo     [0m] [1mApplication startup complete. [0m [[0m[1m[34muvicorn.error[0m][0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:10.618429Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35meb1b3bffacce4a8baa66d652f40ad0cd[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:10.620151Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:59852 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m59852[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35meb1b3bffacce4a8baa66d652f40ad0cd[0m [36mcpu_s[0m=[35m0.002114999999999867[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.004393625014927238[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.004378250043373555[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:10.615707+00:00[0m [36mresponse_start_duration_s[0m=[35m0.004357917001470923[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m422[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://127.0.0.1:5052/v1/chat/completions[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-24T09:05:43.906195Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35m5422a775f6a041f8a6b8133d5c695fb0[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:43.906587Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60009 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m60009[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m5422a775f6a041f8a6b8133d5c695fb0[0m [36mcpu_s[0m=[35m0.00042500000000034177[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.0004303749883547425[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.0004190409672446549[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:43.906120+00:00[0m [36mresponse_start_duration_s[0m=[35m0.0004024579538963735[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m422[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://127.0.0.1:5052/v1/chat/completions[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
[2m2025-09-24T09:05:55.705180Z[0m [[31m[1mcritical [0m] [1mAuth is disabled, all users allowed[0m [[0m[1m[34mcodesuggestions[0m][0m [36mcorrelation_id[0m=[35m85776b45cb084ad8bd6ba3da9ee6a3c0[0m [36mstage[0m=[35mmain[0m [36mtype[0m=[35mmlops[0m
[2m2025-09-24T09:05:55.705536Z[0m [[32m[1minfo     [0m] [1m127.0.0.1:60057 - "POST /v1/chat/completions HTTP/1.1" 422[0m [[0m[1m[34mapi.access[0m][0m [36mclient_ip[0m=[35m127.0.0.1[0m [36mclient_port[0m=[35m60057[0m [36mcontent_type[0m=[35mapplication/json[0m [36mcorrelation_id[0m=[35m85776b45cb084ad8bd6ba3da9ee6a3c0[0m [36mcpu_s[0m=[35m0.00040400000000007097[0m [36mduration_request[0m=[35m-1[0m [36mduration_s[0m=[35m0.000402832985855639[0m [36menabled-instance-verbose-ai-logs[0m=[35mFalse[0m [36mexception_message[0m=[35m"[{'type': 'missing', 'loc': ('body', 'prompt_components'), 'msg': 'Field required', 'input': {'model': 'claude-3-haiku-20240307', 'messages': [{'role': 'user', 'content': 'test'}], 'max_tokens': 50}}]"[0m [36mfirst_chunk_duration_s[0m=[35m0.00039195799035951495[0m [36mgitlab_feature_enabled_by_namespace_ids[0m=[35mNone[0m [36mgitlab_feature_enablement_type[0m=[35mNone[0m [36mgitlab_global_user_id[0m=[35mNone[0m [36mgitlab_host_name[0m=[35mNone[0m [*********************[0m=[35mNone[0m [36mgitlab_language_server_version[0m=[35mNone[0m [36mgitlab_realm[0m=[35mNone[0m [36mgitlab_saas_duo_pro_namespace_ids[0m=[35mNone[0m [36mgitlab_version[0m=[35mNone[0m [36mhttp_version[0m=[35m1.1[0m [36mmethod[0m=[35mPOST[0m [36mpath[0m=[35m/v1/chat/completions[0m [36mrequest_arrived_at[0m=[35m2025-09-24T09:05:55.705107+00:00[0m [36mresponse_start_duration_s[0m=[35m0.00037525000516325235[0m [36mstage[0m=[35mmain[0m [36mstatus_code[0m=[35m422[0m [36mtype[0m=[35mmlops[0m [36murl[0m=[35mhttp://127.0.0.1:5052/v1/chat/completions[0m [36muser_agent[0m=[35mcurl/8.7.1[0m
