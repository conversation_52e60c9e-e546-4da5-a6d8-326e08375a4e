{"id": 127919672, "head_commit_sha": "b6d6f6fd17b52b8cf4e961218c572805e9aa7463", "base_commit_sha": "1f0fa02de1f6b913d674a8be10899fb8540237a9", "start_commit_sha": "1f0fa02de1f6b913d674a8be10899fb8540237a9", "created_at": "2020-12-01T13:59:47.796Z", "merge_request_id": 77101970, "state": "collected", "real_size": "4", "commits": [{"id": "b6d6f6fd17b52b8cf4e961218c572805e9aa7463", "short_id": "b6d6f6fd", "created_at": "2020-12-01T13:59:42.000Z", "parent_ids": [], "title": "added and removed", "message": "added and removed\n", "author_name": "<PERSON>", "author_email": "<EMAIL>", "authored_date": "2020-12-01T13:59:42.000Z", "committer_name": "<PERSON>", "committer_email": "<EMAIL>", "committed_date": "2020-12-01T13:59:42.000Z", "web_url": "https://gitlab.com/viktomas/test-project/-/commit/b6d6f6fd17b52b8cf4e961218c572805e9aa7463"}, {"id": "f36498b3f5ee3e31001774ee639accc7e0b8242c", "short_id": "f36498b3", "created_at": "2020-11-09T14:00:35.000Z", "parent_ids": [], "title": "more changes", "message": "more changes", "author_name": "<PERSON>", "author_email": "<EMAIL>", "authored_date": "2020-11-09T14:00:35.000Z", "committer_name": "<PERSON>", "committer_email": "<EMAIL>", "committed_date": "2020-11-09T14:00:35.000Z", "web_url": "https://gitlab.com/viktomas/test-project/-/commit/f36498b3f5ee3e31001774ee639accc7e0b8242c"}, {"id": "15777945e57262106ea300a3bd4dd098e5f8d0ff", "short_id": "15777945", "created_at": "2020-11-06T13:50:52.000Z", "parent_ids": [], "title": "Update README1.md", "message": "Update README1.md", "author_name": "<PERSON>", "author_email": "<EMAIL>", "authored_date": "2020-11-06T13:50:52.000Z", "committer_name": "<PERSON>", "committer_email": "<EMAIL>", "committed_date": "2020-11-06T13:50:52.000Z", "web_url": "https://gitlab.com/viktomas/test-project/-/commit/15777945e57262106ea300a3bd4dd098e5f8d0ff"}, {"id": "9970d675b33e04edc0a144f973d5171699af026c", "short_id": "9970d675", "created_at": "2020-11-06T09:23:12.000Z", "parent_ids": [], "title": "Update test.js", "message": "Update test.js", "author_name": "<PERSON>", "author_email": "<EMAIL>", "authored_date": "2020-11-06T09:23:12.000Z", "committer_name": "<PERSON>", "committer_email": "<EMAIL>", "committed_date": "2020-11-06T09:23:12.000Z", "web_url": "https://gitlab.com/viktomas/test-project/-/commit/9970d675b33e04edc0a144f973d5171699af026c"}], "diffs": [{"old_path": ".deleted.yml", "new_path": ".deleted.yml", "a_mode": "100644", "b_mode": "0", "new_file": false, "renamed_file": false, "deleted_file": true, "diff": "@@ -1,12 +0,0 @@\n-image: node:12-slim\n-\n-test:\n-    script:\n-        - sleep 20\n-        - echo hello\n-\n-deploy:\n-    stage: deploy\n-    script:\n-        - sleep 20\n-        - echo hello\n\\ No newline at end of file\n"}, {"old_path": "README.md", "new_path": "README1.md", "a_mode": "100644", "b_mode": "100644", "new_file": false, "renamed_file": true, "deleted_file": false, "diff": ""}, {"old_path": "new_file.ts", "new_path": "new_file.ts", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "@@ -0,0 +1,3 @@\n+export class NewFile{\n+    private property: string;\n+}\n"}, {"old_path": "src/test.js", "new_path": "src/test.js", "a_mode": "100644", "b_mode": "100644", "new_file": false, "renamed_file": false, "deleted_file": false, "diff": "@@ -13,14 +13,11 @@\n function containingFunction(){\n     function subFunction(){\n         console.log(\"Some Output\");\n+        console.log(\"Some Output1\");\n     }\n     // Issue is not present when the line after the function name and opening { is empty\n     function subFunction(){\n \n-        console.log(\"OPutput\");\n+        console.log(\"Output\");\n     }\n }\n-\n-function anotherFunction(){\n-    console.log(\"Other Output\");\n-}\n"}, {"old_path": "src/assets/insert-multi-file-snippet.gif", "new_path": "src/assets/insert-multi-file-snippet.gif", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "Binary files /dev/null and b/src/assets/insert-multi-file-snippet.gif differ\n"}, {"old_path": "Screenshot_2020-12-02_at_15.29.33.png", "new_path": "Screenshot.png", "a_mode": "100644", "b_mode": "100644", "new_file": false, "renamed_file": true, "deleted_file": false, "diff": ""}]}