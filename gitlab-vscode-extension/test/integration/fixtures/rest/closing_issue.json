{"assignee": {"avatar_url": "https://secure.gravatar.com/avatar/6042a9152ada74d9fb6a0cdce895337e?s=80&d=identicon", "id": 3457201, "name": "<PERSON>", "state": "active", "username": "v<PERSON><PERSON><PERSON>", "web_url": "https://gitlab.com/viktomas"}, "author": {"avatar_url": "https://secure.gravatar.com/avatar/2c5d6a63b41cbeb3ea4cccda82e758e1?s=80&d=identicon", "id": 2935693, "name": "<PERSON>", "state": "active", "username": "phikai", "web_url": "https://gitlab.com/phikai"}, "created_at": "2020-06-01T17:50:22.893Z", "description": "## Problem to Solve\n\nThe Web IDE is a more complete editing experience that helps to facilitate workflows across multiple files and merge requests. However, users favor the single file editing experience.\n\n## Additional Details\n\nThis is a test that should be setup behind a feature flag to see what kind/if any feedback is generated by making this change.\n\n## Proposal\n\nThe primary and inverted buttons of `Edit` and `Web IDE` should be switched so that Web IDE more clearly looks like a primary action that users should perform.\n\n![Screenshot_2020-06-01_12.42.05](/uploads/31537a7d4f9bca62e7e9ff2c7bafc4ef/Screenshot_2020-06-01_12.42.05.png)\n\n### Feature Flag\n\nThis feature needs to be done with a feature flag and it would be good to have the ability to either assign groups to the feature or assign individual users depending on what we want to do for testing.\n\nWe **SHOULD NOT** enable this by default in the %13.2 release, but rather at a minimum toggle the feature flag on for `gitlab-org` and `gitlab-com`.\n\n[Feature flag implementation documentation](https://docs.gitlab.com/development/feature_flags/)\n\n#### Feature Flag implemenation\n\n!35957 Introduces `web_ide_primary_edit` feature flag that can be anabled for a group.\n\n### Instrumentation\n\nIt would be good as part of this to add telemetry to these buttons to see how many clicks each button receives: https://docs.gitlab.com/development/internal_analytics/internal_event_instrumentation/\n\nThere is also a basic A/B test process documented here: https://docs.gitlab.com/development/experiment_guide/implementing_experiments/\n\n#### Tracking implementation\n\nClicking the edit buttons will trigger the following events.\n\n|  | event | label  | property |\n| ------ | ------ | --- | --- |\n| ![Screenshot_2020-07-08_at_3.09.59_PM](/uploads/84d8a0d045617281eeaaa60dd8cbd676/Screenshot_2020-07-08_at_3.09.59_PM.png) | `click_edit` | `Edit` | |\n| ![Screenshot_2020-07-08_at_3.11.14_PM](/uploads/bbac329ceca6da11c39f7707bb975921/Screenshot_2020-07-08_at_3.11.14_PM.png) | `click_edit` | `Edit` | `secondary` |\n| ![Screenshot_2020-07-08_at_3.11.19_PM](/uploads/ff23cbccbb002e6f9127cb3ddcbd4f36/Screenshot_2020-07-08_at_3.11.19_PM.png) | `click_edit_ide` | `Web IDE` | |\n| ![Screenshot_2020-07-08_at_3.10.13_PM](/uploads/ed647b6f0720bce60b1494908cbc735a/Screenshot_2020-07-08_at_3.10.13_PM.png) | `click_edit_ide` | `Web IDE` | `secondary` |", "id": 35284557, "iid": 219925, "labels": ["Category:Web IDE", "Deliverable", "backstage [DEPRECATED]", "devops::create", "feature flag", "feature::maintenance", "frontend", "group::editor", "missed-deliverable", "missed:13.2", "telemetry", "workflow::verification"], "milestone": {"created_at": "2020-04-09T17:39:21.090Z", "description": "https://about.gitlab.com/releases/", "due_date": "2020-08-17", "group_id": 9970, "id": 1233752, "iid": 50, "start_date": "2020-07-18", "state": "active", "title": "13.3", "updated_at": "2020-07-17T11:45:59.705Z", "web_url": "https://gitlab.com/groups/gitlab-org/-/milestones/50"}, "project_id": 278964, "state": "opened", "title": "Change primary button for editing on files", "updated_at": "2020-07-21T14:26:09.029Z", "user_notes_count": 8}