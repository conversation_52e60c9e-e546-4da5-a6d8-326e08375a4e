{"name": "wdio", "type": "module", "devDependencies": {"@wdio/allure-reporter": "^8.32.4", "@wdio/cli": "^8.26.1", "@wdio/globals": "^8.31.0", "@wdio/json-reporter": "^9.2.14", "@wdio/local-runner": "^8.26.1", "@wdio/mocha-framework": "^8.24.12", "@wdio/spec-reporter": "^8.24.12", "wdio-video-reporter": "^6.1.1", "allure-commandline": "^2.27.0", "uuid": "^9.0.1", "wdio-vscode-service": "^6.1.3", "@influxdata/influxdb-client": "^1.35.0", "winston": "^3.17.0"}, "scripts": {"test:e2e": "wdio run ./wdio.conf.js", "test:e2e:metrics": "node scripts/send_e2e_metrics.js"}}