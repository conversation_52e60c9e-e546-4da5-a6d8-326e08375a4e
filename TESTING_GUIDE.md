# GitLab VSCode Extension Testing Guide

## Quick Start

### 1. Fix GitLab Instance Connection

Your extension is currently connecting to `https://gitlab.com` instead of your local instance. Here's how to fix it:

**In the Extension Development Host window (after pressing F5):**

1. Open Command Palette (`Cmd/Ctrl + Shift + P`)
2. Type `Preferences: Open Settings (JSON)`
3. Add these settings:

```json
{
  "gitlab.instanceUrl": "http://localhost:3000",
  "gitlab.debug": true,
  "gitlab-lsp.trace.server": "verbose",
  "gitlab.duoAgentPlatform.enabled": true,
  "gitlab.duoAgentPlatform.connectionType": "grpc"
}
```

4. Remove existing GitLab account: `GitLab: Remove Account from VS Code`
5. Add local account: `GitLab: Add Account to VS Code`
   - URL: `http://localhost:3000`
   - Username: `root`
   - Password: Your GDK root password

### 2. Launch with Enhanced Logging

1. In your extension project, press **F5**
2. Select **"Debug Extension with Duo Platform"** (this has enhanced logging enabled)
3. A new VSCode window opens (Extension Development Host)

### 3. Monitor All Logs

Run the monitoring script:
```bash
./test-gitlab-extension.sh start
```

This will:
- Check if all services are running
- Start monitoring AI Gateway and Duo Workflow Service logs
- Show you where to find extension logs

## Detailed Logging Setup

### Extension Logs
- **Location**: VSCode Output panel → "GitLab Workflow"
- **Enhanced logging**: Enabled with `VSCODE_GITLAB_VERBOSE_LOGGING=true`
- **Shows**: API requests, responses, headers, request/response bodies

### AI Gateway Logs
- **Location**: `gitlab-development-kit/gitlab-ai-gateway/ai-gateway.log`
- **Configuration**: Already set to debug level in `.env`
- **Shows**: All AI Gateway requests, tool calls, model interactions

### Duo Workflow Service Logs
- **Location**: `gitlab-development-kit/gitlab-ai-gateway/********************/service.log`
- **Configuration**: Debug level enabled
- **Shows**: Workflow execution, gRPC calls, tool invocations

## Testing Workflow

### 1. Basic Connection Test
```bash
./test-gitlab-extension.sh status
```

### 2. Launch Extension
- Press F5 → "Debug Extension with Duo Platform"
- Open test project: `/Users/<USER>/test-gitlab-extension`

### 3. Test Features

**GitLab Duo Chat:**
- Click GitLab Duo Chat icon in Activity Bar
- Ask: "Explain this code" while having a file open
- Use `Alt+C` for Quick Chat

**Code Suggestions:**
- Open a code file
- Start typing - should see suggestions from your local instance

**API Monitoring:**
- Watch Extension Logs for requests to `http://localhost:3000`
- Should see detailed request/response logging

### 4. Tool Call Monitoring

When using GitLab Duo Chat, you'll see:

**In Extension Logs:**
```
fetch: POST http://localhost:3000/api/v4/...
fetch: request body: {"query": "...", "variables": {...}}
fetch: response body: {"data": {...}}
```

**In AI Gateway Logs:**
```
[DEBUG] Tool call: issue_reader
[DEBUG] Tool input: {...}
[DEBUG] Tool response: {...}
```

**In Duo Workflow Service Logs:**
```
[DEBUG] Workflow execution started
[DEBUG] gRPC call to GitLab API
[DEBUG] Tool invocation result
```

## Troubleshooting

### Extension Still Connects to gitlab.com
1. Verify settings in Extension Development Host window (not main window)
2. Remove and re-add GitLab account
3. Check Extension Logs for connection attempts

### No Detailed Logs
1. Ensure `gitlab.debug: true` in Extension Development Host settings
2. Use "Debug Extension with Duo Platform" launch configuration
3. Check Output panel → "GitLab Workflow"

### Services Not Running
```bash
# Check GitLab
curl -I http://localhost:3000

# Check AI Gateway
curl -I http://localhost:5052/monitoring/healthz

# Check Duo Workflow Service
lsof -i :50051
```

## Log Analysis

### Finding Tool Calls
Look for these patterns in logs:

**Extension Logs:**
- `fetch: POST http://localhost:3000/api/graphql`
- `fetch: request body:` (contains GraphQL queries)

**AI Gateway Logs:**
- `Tool call:` (shows which tools are being invoked)
- `Tool input:` (shows tool parameters)
- `Tool response:` (shows tool results)

**Duo Workflow Service Logs:**
- `Workflow execution` (shows workflow steps)
- `gRPC call` (shows API interactions)

### Performance Monitoring
- Request duration in extension logs: `after X ms`
- Tool execution time in AI Gateway logs
- Workflow step timing in Duo Workflow Service logs

## Advanced Configuration

### Enable Request/Response Logging in AI Gateway
Edit `gitlab-development-kit/gitlab-ai-gateway/.env`:
```bash
AIGW_LOGGING__ENABLE_REQUEST_LOGGING=true
AIGW_LOGGING__ENABLE_LITELLM_LOGGING=true
```

### Enable gRPC Tracing
Edit `gitlab-development-kit/gitlab-ai-gateway/********************/.env`:
```bash
GRPC_TRACE=http_keepalive,call_error,connectivity_state,channel
```

### LangChain Tracing
Both services have LangChain tracing enabled:
```bash
LANGCHAIN_TRACING_V2=true
LANGSMITH_PROJECT=duo-workflow-local-debug
```

## Useful Commands

```bash
# Start monitoring
./test-gitlab-extension.sh start

# Check service status
./test-gitlab-extension.sh status

# Stop monitoring
./test-gitlab-extension.sh stop

# View available logs
./test-gitlab-extension.sh logs

# Watch AI Gateway logs in real-time
tail -f gitlab-development-kit/gitlab-ai-gateway/ai-gateway.log

# Watch Duo Workflow Service logs
tail -f gitlab-development-kit/gitlab-ai-gateway/********************/service.log
```
