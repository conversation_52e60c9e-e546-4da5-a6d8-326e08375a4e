# pylint: disable=direct-environment-variable-reference

import os
import structlog
from langsmith import Client

log = structlog.stdlib.get_logger("langsmith_setup")


def setup_langsmith():
    """Initialize LangSmith tracing for the Duo Workflow Service."""

    print("🔍 LangSmith setup function called!")  # Debug print
    log.info("LangSmith setup function called")

    # Check if LangSmith tracing is enabled
    langsmith_tracing = os.environ.get("LANGSMITH_TRACING", "false").lower()
    langchain_tracing = os.environ.get("LANGCHAIN_TRACING_V2", "false").lower()

    print(f"🔍 LANGSMITH_TRACING={langsmith_tracing}, LANGCHAIN_TRACING_V2={langchain_tracing}")  # Debug print

    if langsmith_tracing != "true" and langchain_tracing != "true":
        print("❌ LangSmith tracing is disabled")  # Debug print
        log.info("LangSmith tracing is disabled")
        return
    
    # Get LangSmith configuration
    api_key = os.environ.get("LANGSMITH_API_KEY") or os.environ.get("LANGCHAIN_API_KEY")
    project = os.environ.get("LANGSMITH_PROJECT") or os.environ.get("LANGCHAIN_PROJECT")
    endpoint = os.environ.get("LANGSMITH_ENDPOINT") or os.environ.get("LANGCHAIN_ENDPOINT")
    
    if not api_key:
        log.warning("LangSmith tracing enabled but no API key found. Set LANGSMITH_API_KEY or LANGCHAIN_API_KEY")
        return
    
    if not project:
        log.warning("LangSmith tracing enabled but no project found. Set LANGSMITH_PROJECT or LANGCHAIN_PROJECT")
        return
    
    try:
        # Initialize LangSmith client to verify connection
        client = Client(
            api_key=api_key,
            api_url=endpoint or "https://api.smith.langchain.com"
        )
        
        # Test the connection
        client.info()
        
        log.info(
            "LangSmith tracing initialized successfully",
            project=project,
            endpoint=endpoint or "https://api.smith.langchain.com",
            api_key_prefix=api_key[:10] + "..." if api_key else None
        )
        
        # Set additional environment variables for consistency
        if not os.environ.get("LANGCHAIN_TRACING_V2"):
            os.environ["LANGCHAIN_TRACING_V2"] = "true"
        if not os.environ.get("LANGCHAIN_API_KEY"):
            os.environ["LANGCHAIN_API_KEY"] = api_key
        if not os.environ.get("LANGCHAIN_PROJECT"):
            os.environ["LANGCHAIN_PROJECT"] = project
        if endpoint and not os.environ.get("LANGCHAIN_ENDPOINT"):
            os.environ["LANGCHAIN_ENDPOINT"] = endpoint
            
        # Set LangSmith specific environment variables
        if not os.environ.get("LANGSMITH_TRACING"):
            os.environ["LANGSMITH_TRACING"] = "true"
        if not os.environ.get("LANGSMITH_API_KEY"):
            os.environ["LANGSMITH_API_KEY"] = api_key
        if not os.environ.get("LANGSMITH_PROJECT"):
            os.environ["LANGSMITH_PROJECT"] = project
        if endpoint and not os.environ.get("LANGSMITH_ENDPOINT"):
            os.environ["LANGSMITH_ENDPOINT"] = endpoint
            
        # Configure additional LangSmith settings
        sampling_rate = os.environ.get("LANGSMITH_TRACING_SAMPLING_RATE", "1.0")
        hide_inputs = os.environ.get("LANGSMITH_HIDE_INPUTS", "false").lower()
        hide_outputs = os.environ.get("LANGSMITH_HIDE_OUTPUTS", "false").lower()
        background_callbacks = os.environ.get("LANGCHAIN_CALLBACKS_BACKGROUND", "true").lower()
        
        log.info(
            "LangSmith configuration applied",
            sampling_rate=sampling_rate,
            hide_inputs=hide_inputs == "true",
            hide_outputs=hide_outputs == "true",
            background_callbacks=background_callbacks == "true"
        )
        
    except Exception as e:
        log.error(
            "Failed to initialize LangSmith tracing",
            error=str(e),
            project=project,
            endpoint=endpoint or "https://api.smith.langchain.com"
        )
        # Don't raise the exception to avoid breaking service startup
        # Just log the error and continue without tracing
