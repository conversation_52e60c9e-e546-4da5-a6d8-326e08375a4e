#!/usr/bin/env bash

# This is the GDK one line installation. For more information, please visit:
# https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/index.md#one-line-installation
#
# Wrap everything in a function to ensure a partially downloaded install script
# is not executed. Inspired by https://install.sandstorm.io/
#
# Valid args are:
#
# 1 = directory in which to clone into, default is gdk (GDK_INSTALL_DIR)
# 2 = git SHA/branch to checkout once cloned, default is main (GDK_CLONE_BRANCH)
# 3 = enable telemetry, default is false (ENABLE_TELEMETRY)
#
# Example usage with arguments:
#
#   curl "https://gitlab.com/gitlab-org/gitlab-development-kit/-/raw/main/support/install" | bash -s gdk my-gdk-branch-name true
#
_() {

set -eo pipefail

DEFAULT_GDK_INSTALL_DIR="gdk"
DEFAULT_GDK_REPO_URL="https://gitlab.com/gitlab-org/gitlab-development-kit.git"
DEFAULT_GITLAB_REPO_URL="https://gitlab.com/gitlab-org/gitlab.git"

REQUIRED_COMMANDS=(git make)

error() {
  echo "ERROR: ${1}" >&2
  exit 1
}

ensure_required_commands_exist() {
  for command in "${REQUIRED_COMMANDS[@]}"; do
    if ! command -v "${command}" > /dev/null 2>&1; then
      error "Please ensure ${command} is installed."
    fi
  done
}

ensure_not_root() {
  if [[ ${EUID} -eq 0 ]]; then
    return 1
  fi

  return 0
}

clone_gdk_if_needed() {
  if [[ -d ${GDK_INSTALL_DIR} ]]; then
    echo "INFO: A ${GDK_INSTALL_DIR} directory already exists in the current working directory, resuming.."
  else
    git clone "${GDK_REPO_URL}" "${GDK_INSTALL_DIR}"
  fi
}

ensure_gdk_clone_branch_checked_out() {
  git -C "${PWD}/${GDK_INSTALL_DIR}" fetch origin "${GDK_CLONE_BRANCH}"
  git -C "${PWD}/${GDK_INSTALL_DIR}" checkout "${GDK_CLONE_BRANCH}"
}

setup_tool_version_manager() {
  local gdk_yml="${PWD}/gdk.yml"

  echo "INFO: Configuring mise as tool version manager."

  mkdir -p "$(dirname "${gdk_yml}")"

  cat << EOF > "${gdk_yml}"
---
tool_version_manager:
  enabled: true
EOF

  local full_path
  full_path=$(readlink -f "${gdk_yml}")

  echo "INFO: Tool version manager settings saved to ${full_path}:"
  cat "${gdk_yml}"
}

bootstrap() {
  make bootstrap
}

gdk_install() {
  echo "Running 'gdk install' using mise.."
  mise exec -- gdk install gitlab_repo="$GITLAB_REPO_URL" telemetry_enabled="$ENABLE_TELEMETRY"
}

echo
echo "INFO: This is the GDK one line installation. For more information, please visit:"
echo "INFO: https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/index.md#one-line-installation"
echo "INFO:"
echo "INFO: The source for the installation script can be viewed at:"
echo "INFO: https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/support/install"
echo

if [ $# -eq 1 ]; then
  echo "Where would you like to install the GDK? [./${DEFAULT_GDK_INSTALL_DIR}]"
  read -r GDK_INSTALL_DIR </dev/tty
  echo
  echo "Which GitLab repo URL would you like to clone? [${DEFAULT_GITLAB_REPO_URL}]"
  echo
  echo "ATTENTION: For members of the wider community, it is recommended to use the community fork (https://gitlab.com/gitlab-community/gitlab-org/gitlab.git)."
  echo "See https://gitlab.com/gitlab-community/meta for instructions on how to join."
  echo "If you'd prefer to use your own repository, please ensure that its visibility is set to public."
  read -r GITLAB_REPO_URL </dev/tty
  echo
  echo "To improve GDK, GitLab would like to collect basic error and usage, including your platform and architecture."
  echo
  echo "Would you like to send telemetry anonymously to GitLab? [y/N]:"
  read -r consent </dev/tty
  case "$consent" in
    [yY]) ENABLE_TELEMETRY="true" ;;
    *)    ENABLE_TELEMETRY="false" ;;
  esac
else
  # Note: Passing arguments this way is meant for CI.
  # If you're running this manually, don't rely on the argument order as it may change.
  GDK_INSTALL_DIR="${2-gdk}"
  GDK_CLONE_BRANCH="${3-main}"
  ENABLE_TELEMETRY="${4-false}"
fi

# Set defaults for any unset variables.
GDK_INSTALL_DIR=${GDK_INSTALL_DIR:-${DEFAULT_GDK_INSTALL_DIR}}
GDK_CLONE_BRANCH=${GDK_CLONE_BRANCH:-main}
GITLAB_REPO_URL=${GITLAB_REPO_URL:-${DEFAULT_GITLAB_REPO_URL}}
GDK_REPO_URL=${GDK_REPO_URL:-${DEFAULT_GDK_REPO_URL}}

if ! ensure_not_root; then
  error "Running as root is not supported."
fi

ensure_required_commands_exist
clone_gdk_if_needed
ensure_gdk_clone_branch_checked_out
cd "${GDK_INSTALL_DIR}" || exit
setup_tool_version_manager
bootstrap
gdk_install

echo "INFO: To ensure you're in the newly installed GDK directory, please run:"
echo "cd ${GDK_INSTALL_DIR}"
echo
}

# If we've reached here, the entire install script has been downloaded and
# "should" be safe to execute.
_ "$0" "$@"
