#!/usr/bin/env bash

set -euo pipefail

parent_path=$(dirname "$0")

# shellcheck source=support/bootstrap-common.sh
source "${parent_path}"/bootstrap-common.sh


###############################################################################
if ! common_preflight_checks; then
  error "Failed to perform preflight checks." >&2
fi

if ! setup_platform; then
  error "Failed to install set up platform." >&2
fi
