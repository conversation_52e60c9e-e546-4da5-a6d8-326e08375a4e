#!/bin/bash -e

echo "DEFAULT_BRANCH_IMAGE: ${DEFAULT_BRANCH_IMAGE}"
echo "DEFAULT_MAIN_IMAGE: ${DEFAULT_MAIN_IMAGE}"

case "$1" in
    ci-login)
        echo "Logging into ${CI_REGISTRY}"
        docker login --username gitlab-ci-token --password "${CI_JOB_TOKEN}" "${CI_REGISTRY}"
        ;;

    ci-logout)
        echo "Logging out"
        docker logout "${CI_REGISTRY}"
        ;;

    ci-build-if-necessary)
        # We only want to rebuild the image if these files change.
        files_to_hash=(
          support/bootstrap
          support/bootstrap-common.sh
          Dockerfile
          packages_ubuntu.txt
          .tool-versions
          .mise-version
          mise.toml
        )
        # Create a shasum of all these files to use as the image tag.
        # Other MRs that don't touch these files can benefit from this cache image.
        dependency_sha=$(cat "${files_to_hash[@]}" | sha256sum | cut -d ' ' -f 1)

        image_prefix="mise-bootstrapped-verify"
        cache_image="${DEFAULT_IMAGE_WITHOUT_TAG}:${dependency_sha}"
        default_destinations="--tag ${cache_image} --tag ${DEFAULT_BRANCH_IMAGE}"
        bootstrapped_image="${CI_REGISTRY_IMAGE}/${image_prefix}"
        main_branch_destinations="--tag ${bootstrapped_image}/main:${CI_COMMIT_SHA} --tag ${bootstrapped_image}/main:latest"

        # We push verify image from main branch to separate path and tag with commit sha so we are able to properly
        #   pin image versions in 'gitlab' canonical project
        # Separate path allows dependency updaters to not be confused by branch specific tags
        if [ "$DEFAULT_BRANCH_IMAGE" == "${bootstrapped_image}:main" ]; then
          # Re-tag image with git sha on no-op changes so that each commit sha in default branch has corresponding tag
          if [ "$RETAG_LATEST_RELEASE" == "true" ]; then
            destinations="${main_branch_destinations}"
          else
            destinations="${default_destinations} ${main_branch_destinations}"
          fi
        fi

        # We use the cache from the image with the same shasum first.
        # If it doesn't exist, we use the cache from the branch image.
        # If it doesn't exist, we use the cache from the `main` image.
        # shellcheck disable=SC2086
        docker buildx build \
          --provenance=false \
          --cache-to=type=inline \
          --cache-from="${cache_image}" \
          --cache-from="${DEFAULT_BRANCH_IMAGE}" \
          --cache-from="${DEFAULT_MAIN_IMAGE}" \
          --build-arg CI="$CI" \
          ${destinations:-$default_destinations} \
          --push \
          --file="Dockerfile" \
          .
        ;;

    ci-build-verify-image)
        mkdir -p "${GITLAB_CI_CACHE_FULL_DIR}"

        docker buildx build \
          --provenance=false \
          --cache-to=type=inline \
          --cache-from="${VERIFY_IMAGE_MAIN}" \
          --build-arg from_image="${DEFAULT_BRANCH_IMAGE}" \
          --build-arg CI="$CI" \
          --build-arg PROJECT_URL="${CI_MERGE_REQUEST_SOURCE_PROJECT_URL:-${CI_PROJECT_URL}}" \
          --build-arg SHA="${CI_MERGE_REQUEST_SOURCE_BRANCH_SHA:-${CI_COMMIT_SHA}}" \
          --build-arg GITLAB_CI_CACHE_DIR="${GITLAB_CI_CACHE_DIR}" \
          --build-arg GITLAB_CI_CACHE_GO_DIR="${GITLAB_CI_CACHE_GO_DIR}" \
          --build-arg GDK_INTERNAL_CACHE_FULL_DIR="${GDK_INTERNAL_CACHE_FULL_DIR}" \
          --build-arg BUNDLE_PATH="${BUNDLE_PATH}" \
          --build-arg GEM_HOME="${GEM_HOME}" \
          --build-arg GEM_PATH="${GEM_PATH}" \
          --build-arg GOCACHE="${GOCACHE}" \
          --build-arg GOMODCACHE="${GOMODCACHE}" \
          --build-arg NODE_PATH="${NODE_PATH}" \
          --build-arg PUMA_SINGLE_MODE="${PUMA_SINGLE_MODE}" \
          --build-arg GDK_DEBUG="${GDK_DEBUG}" \
          --file="support/ci/Dockerfile.verify" \
          --tag "${VERIFY_IMAGE}" \
          --push \
          .

        ;;

    ci-build-integration-image)
        docker buildx build \
          --provenance=false \
          --cache-to=type=inline \
          --cache-from="${VERIFY_IMAGE_MAIN}" \
          --build-arg from_image="${VERIFY_IMAGE_MAIN}" \
          --build-arg CI="$CI" \
          --build-arg SHA="${CI_MERGE_REQUEST_SOURCE_BRANCH_SHA:-${CI_COMMIT_SHA}}" \
          --build-arg REPO_URL="${CI_REPOSITORY_URL}" \
          --build-arg GITLAB_CI_CACHE_DIR="${GITLAB_CI_CACHE_DIR}" \
          --build-arg GITLAB_CI_CACHE_GO_DIR="${GITLAB_CI_CACHE_GO_DIR}" \
          --build-arg GDK_DEBUG="${GDK_DEBUG}" \
          --file="support/ci/Dockerfile.integration" \
          --tag "${INTEGRATION_IMAGE}" \
          --push \
          .

        ;;

    ci-build-novel-image)
        build_os_version="$2"

        if [ "$build_os_version" == "" ]; then
          echo "usage: $0 <build os version>"
          echo "  Supported OS versions:"
          find support/ci/Dockerfile.novel_* | sed 's/.*_/  - /g'
          exit 1
        fi

        docker buildx build \
          --build-arg BRANCH="${CI_COMMIT_REF_NAME}" \
          --file "support/ci/Dockerfile.novel_${build_os_version}" \
          .
        ;;

    build)
        docker buildx build \
          --provenance=false \
          --build-arg CI="$CI" \
          --tag "${DEFAULT_BRANCH_IMAGE}" \
          .
        ;;

    *)
        echo "Usage: $0 [ci-login|ci-logout|ci-build-if-necessary|ci-build-verify-image|ci-build-novel-image|build]"
        exit 1
        ;;
esac
