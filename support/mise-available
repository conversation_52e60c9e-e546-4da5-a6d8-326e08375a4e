#!/usr/bin/env bash

set -euo pipefail

CDPATH=''
ROOT_PATH="$(cd "$(dirname "${BASH_SOURCE[${#BASH_SOURCE[@]} - 1]}")/.." || exit ; pwd -P)"
# Borrowed from https://stackoverflow.com/a/76156715
MISE_ENABLED=$(awk -v k1=mise -v k2=enabled 'BEGIN{FS=":[[:space:]]*"}/^[[:alpha:]]/{k=$0;next}{$0=k $0}k1==$1&&k2==$2{f=1;print $3}END{if(!f){print "false"}}' "${ROOT_PATH}/gdk.yml" 2>/dev/null || echo 'false')
MISE=$(command -v mise 2>/dev/null || true)

if [ "${MISE_ENABLED}" = "true" ] && [ -n "${MISE}" ]; then
  echo true
else
  echo false
fi
