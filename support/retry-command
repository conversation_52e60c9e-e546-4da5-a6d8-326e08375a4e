#!/usr/bin/env ruby
# frozen_string_literal: true

exit(0) if ARGV.empty?

require_relative '../lib/gdk'

display_output = ENV.fetch('GDK_EXECUTE_DISPLAY_OUTPUT', GDK::Shellout::DEFAULT_EXECUTE_DISPLAY_OUTPUT).to_s == 'true'
retry_attempts = ENV.fetch('GDK_EXECUTE_RETRY_ATTEMPTS', 3).to_i
retry_delay_secs = ENV.fetch('GDK_EXECUTE_RETRY_DELAY_SECS', GDK::Shellout::DEFAULT_EXECUTE_RETRY_DELAY_SECS).to_i

exit(GDK::Shellout.new(ARGV).execute(retry_attempts: retry_attempts, retry_delay_secs: retry_delay_secs, display_output: display_output).success?)
