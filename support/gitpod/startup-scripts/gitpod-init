#!/usr/bin/env bash

# This script is executed during the Gitpod prebuild
# or on workspace start (when no prebuild is available)
# https://www.gitpod.io/docs/config-start-tasks/

set -eo pipefail

# shellcheck disable=SC1091
source "$HOME/gitpod-common"

if ! is_gitpod_instance; then
  sudo mkdir -p /workspace
  sudo chown gitpod /workspace
  log "Cloning GitLab"
  git clone --depth 1 https://gitlab.com/gitlab-org/gitlab.git /workspace/gitlab
  export GITPOD_REPO_ROOT="/workspace/gitlab"
fi

log "Copying GDK"
cp -r "$HOME/gitlab-development-kit" /workspace
cd /workspace/gitlab-development-kit

# Ensure GitLab directory is symlinked under the GDK
ln -nfs "$GITPOD_REPO_ROOT" /workspace/gitlab-development-kit/gitlab
mv /workspace/gitlab-development-kit/secrets.yml /workspace/gitlab-development-kit/gitlab/config

# Reconfigure GDK
log "Reconfiguring GDK"
gdk reconfigure

# Run DB migrations
log "Running DB migrations"
bundle exec rake gitlab-db-migrate

# Stop GDK
log "Stopping GDK"
gdk stop
log "GDK stopped"
