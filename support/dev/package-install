#!/usr/bin/env bash

set -euo pipefail

execute_cmd() {
  local cmd="${1}"

  [[ "${GDK_DEBUG:-false}" == "true" ]] && echo "DEBUG: cmd=[${cmd}]"
  eval "${cmd}"
}

brew_install() {
  local package="${1}"

  execute_cmd "brew install ${package}"
}

apt_install() {
  local package="${1}"

  execute_cmd "apt-get install -y ${package}"
}

pacman_install() {
  local package="${1}"

  execute_cmd "pacman --noconfirm -Sy ${package}"
}

dnf_install() {
  local package="${1}"

  execute_cmd "dnf install -y ${package}"
}

install_package() {
  local package="${1}"

  echo "INFO: Attempting to install '${package}'"

  $(install_function_for_platform) "${package}"
}

install_function_for_platform() {
  # Borrowed some parts from https://github.com/keithieopia/piu
  #
  if [[ "${OSTYPE}" == "darwin"* ]]; then
    if command -v brew > /dev/null 2>&1; then
      echo "brew_install"

      return 0
    fi
  elif [[ "${OSTYPE}" == "linux-gnu"* ]]; then
    if grep -q arch /etc/os-release; then
      echo "pacman_install"

      return 0
    elif [[ -f /etc/debian_version ]]; then
      echo "apt_install"

      return 0
    elif grep -q fedora /etc/os-release; then
      echo "dnf_install"

      return 0
    fi
  fi

  return 1
}

if ! install_function_for_platform > /dev/null 2>&1; then
  echo "ERROR: Unsupported platform." >&2
  exit 1
fi

install_package "$@"
