#!/usr/bin/env ruby
#
# frozen_string_literal: true

# This script implements 'cd foo && exec bar' without the need to invoke
# /bin/sh for the ' && '.

require_relative '../lib/gdk'

abort "Usage: #{$PROGRAM_NAME} DIRECTORY COMMAND [ARGUMENTS ...]" if ARGV.count < 2

Dir.chdir(ARGV.shift)

cmd = ARGV
cmd = %w[mise exec --] + cmd if GDK::Dependencies.tool_version_manager_available?

if GDK::Dependencies.bundler_loaded?
  Bundler.with_unbundled_env do
    exec(*cmd)
  end
else
  exec(*cmd)
end
