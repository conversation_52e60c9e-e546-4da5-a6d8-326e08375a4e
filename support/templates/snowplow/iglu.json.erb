{"schema": "iglu:com.snowplowanalytics.iglu/resolver-config/jsonschema/1-0-2", "data": {"cacheSize": 2000, "cacheTtl": 3600, "repositories": [{"name": "Iglu Central", "priority": 0, "vendorPrefixes": ["com.snowplowanalytics"], "connection": {"http": {"uri": "http://iglucentral.com"}}}, {"name": "Iglu Central - GCP Mirror", "priority": 1, "vendorPrefixes": ["com.snowplowanalytics"], "connection": {"http": {"uri": "http://mirror01.iglucentral.com"}}}, {"name": "<PERSON><PERSON><PERSON> Gitlab", "priority": 0, "vendorPrefixes": ["com.gitlab"], "connection": {"http": {"uri": "https://gitlab-org.gitlab.io/iglu"}}}]}}