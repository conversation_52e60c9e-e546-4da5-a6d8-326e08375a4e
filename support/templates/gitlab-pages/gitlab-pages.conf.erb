listen-http=<%= config.gitlab_pages.__uri %>
gitlab-server=<%= config.__uri %>
artifacts-server=<%= config.__uri.merge('api/v4') %>
pages-root=<%= gdk_root.join('gitlab', 'shared', 'pages') %>
pages-domain=<%= config.gitlab_pages.host %>
api-secret-key=<%= config.gitlab_pages.secret_file %>
log-verbose=<%= config.gitlab_pages.verbose %>
propagate-correlation-id=<%= config.gitlab_pages.propagate_correlation_id %>
<%- if config.gitlab_pages.access_control? -%>
auth-client-id=<%= config.gitlab_pages.auth_client_id %>
auth-client-secret=<%= config.gitlab_pages.auth_client_secret %>
auth-secret=<%= config.gitlab_pages.__auth_secret %>
auth-redirect-uri=<%= config.gitlab_pages.__auth_redirect_uri %>
auth-scope=<%= config.gitlab_pages.auth_scope %>
<%- end -%>
<%- if config.https? %>
client-ca-certs=<%= gdk_root.join(config.nginx.ssl.certificate) %>
<%- end -%>
