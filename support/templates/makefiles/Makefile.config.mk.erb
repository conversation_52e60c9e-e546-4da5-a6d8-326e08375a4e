# ---------------------------------------------------------------------------------------------
# This file is used by the GDK to get interoperability between <PERSON> and <PERSON><PERSON> with the end
# goal of getting rid of <PERSON> in the future: https://gitlab.com/groups/gitlab-org/-/epics/1556.
# This file can be generated with the `rake support/makefiles/Makefile.config.mk` task.
# ---------------------------------------------------------------------------------------------

<%- tasks.each do |task| -%>
<%- if task.no_op_condition -%>
.PHONY: <%= task.name %>
<%= task.name %>:<%= task.make_dependencies.empty? ? '' : " #{task.make_dependencies}" %>
ifeq ($(<%= task.no_op_condition %>),true)
<%= "\t" %>$(Q)rake <%= task.name %>
else
<%= "\t" %>@true
endif

<%- elsif task.timed -%>
.PHONY: <%= task.name %>
<%= task.name %>: <%= task.name %>-timed

.PHONY: <%= task.name %>-run
<%= task.name %>-run: rake
<%= "\t" %>$(Q)rake <%= task.name %>

<%- else -%>
.PHONY: <%= task.name %>
<%= task.name %>:<%= task.make_dependencies.empty? ? '' : " #{task.make_dependencies}"  %>
<%= "\t" %>$(Q)rake <%= task.name %>

<%- end -%>
<%- end -%>
