# frozen_string_literal: true

# -----------------------------------------------------------------------
# This file is used by the GDK to generate a default config/puma.rb file
# Note that `<%= gdk_root %>` will be substituted for the actual GDK root
# directory when this file is generated
# -----------------------------------------------------------------------

# Load "path" as a rackup file.
#
# The default is "config.ru".
#
rackup '<%= config.gitlab.dir %>/config.ru'
pidfile '<%= config.gitlab.dir %>/tmp/pids/puma.pid'
state_path '<%= config.gitlab.dir %>/tmp/pids/puma.state'
directory '<%= config.gitlab.dir %>/tmp'

## Uncomment the lines if you would like to write puma stdout & stderr streams
## to a different location than rails logs.
## When using GitLab Development Kit, by default, these logs will be consumed
## by runit and can be accessed using `gdk tail rails-web`
# stdout_redirect '<%= config.gitlab.dir %>/log/puma.stdout.log',
#  '<%= config.gitlab.dir %>/log/puma.stderr.log',
#  true

# Configure "min" to be the minimum number of threads to use to answer
# requests and "max" the maximum.
#
# The default is "0, 16".
#
threads <%= config.gitlab.rails.puma.__threads_min %>, <%= config.gitlab.rails.puma.__threads_max %>

# By default, workers accept all requests and queue them to pass to handlers.
# When false, workers accept the number of simultaneous requests configured.
#
# Queueing requests generally improves performance, but can cause deadlocks if
# the app is waiting on a request to itself. See https://github.com/puma/puma/issues/612
#
# When set to false this may require a reverse proxy to handle slow clients and
# queue requests before they reach puma. This is due to disabling HTTP keepalive
queue_requests false

# Bind the server to "url". "tcp://", "unix://" and "ssl://" are the only
# accepted protocols.
bind '<%= config.gitlab.rails.__bind %>'

if ENV['PUMA_SINGLE_MODE']
  # single worker/thread to support using `binding.pry` for debugging
  workers 0
  threads 0, 1
else
  workers <%= config.gitlab.rails.puma.workers %>
end

require_relative "<%= config.gitlab.dir %>/lib/gitlab/cluster/lifecycle_events"

if Gem::Version.new(Puma::Const::PUMA_VERSION) < Gem::Version.new('7.0')
  Gitlab::Cluster::LifecycleEvents.set_puma_options @config.options

  on_restart do
    # Signal application hooks that we're about to restart
    Gitlab::Cluster::LifecycleEvents.do_before_master_restart
  end

  on_worker_boot do
    # Signal application hooks of worker start
    Gitlab::Cluster::LifecycleEvents.do_worker_start
  end

  on_worker_shutdown do
    # Signal application hooks that a worker is shutting down
    Gitlab::Cluster::LifecycleEvents.do_worker_stop
  end
else
  Gitlab::Cluster::LifecycleEvents.*********************(<%= config.gitlab.rails.puma.workers %>)

  before_restart do
    # Signal application hooks that we're about to restart
    Gitlab::Cluster::LifecycleEvents.do_before_master_restart
  end

  before_worker_boot do
    # Signal application hooks of worker start
    Gitlab::Cluster::LifecycleEvents.do_worker_start
  end

  before_worker_shutdown do
    # Signal application hooks that a worker is shutting down
    Gitlab::Cluster::LifecycleEvents.do_worker_stop
  end
end

before_fork do
  # Signal application hooks that we're about to fork
  Gitlab::Cluster::LifecycleEvents.do_before_fork
end

# Preload the application before starting the workers; this conflicts with
# phased restart feature. (off by default)

preload_app!

tag 'gitlab-puma-worker'

# Verifies that all workers have checked in to the master process within
# the given timeout. If not the worker process will be restarted. Default
# value is 60 seconds.
#
worker_timeout ENV.fetch('PUMA_WORKER_TIMEOUT', 60)

worker_boot_timeout ENV.fetch('PUMA_WORKER_BOOT_TIMEOUT', 60)

# https://github.com/puma/puma/blob/master/5.0-Upgrade.md#lower-latency-better-throughput
wait_for_less_busy_worker ENV.fetch('PUMA_WAIT_FOR_LESS_BUSY_WORKER', 0.001).to_f

# Use json formatter
require_relative "<%= config.gitlab.dir %>/lib/gitlab/puma_logging/json_formatter"

json_formatter = Gitlab::PumaLogging::JSONFormatter.new
log_formatter do |str|
  json_formatter.call(str)
end

# Maintain backwards compatibility with older GitLab branches. Don't
# fail if the error handler can't be loaded. This workaround
# can be removed after GitLab 16.8.
begin
  require_relative "<%= config.gitlab.dir %>/lib/gitlab/puma/error_handler"

  error_handler = Gitlab::Puma::ErrorHandler.new(ENV['RAILS_ENV'] == 'production')

  lowlevel_error_handler do |ex, env, status_code|
    error_handler.execute(ex, env, status_code)
  end
rescue LoadError
end
