application_identifier: "gdkconsumer"
queueing:
  driver: "nats"
  url: "localhost:4222"
  stream_name: "siphon_stream_main_db"
streams:
  <%- config.siphon.tables.each do |table| -%>
  - identifier: <%= table %>
    subject: <%= table %>
    target: siphon_<%= table %>
  <%- end -%>

clickhouse:
  host: localhost
  port: <%= config.clickhouse.tcp_port %>
  user: default
  database: gitlab_clickhouse_development
prometheus:
  port: 8084
