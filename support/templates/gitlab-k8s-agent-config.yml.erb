agent:
  listen:
    network: "<%= config.gitlab_k8s_agent.agent_listen_network %>"
    address: "<%= config.gitlab_k8s_agent.agent_listen_address %>"
    websocket: <%= config.gitlab_k8s_agent.__agent_listen_websocket %>
  kubernetes_api:
    listen:
      network: "<%= config.gitlab_k8s_agent.k8s_api_listen_network %>"
      address: "<%= config.gitlab_k8s_agent.k8s_api_listen_address %>"
    url_path_prefix: "<%= config.gitlab_k8s_agent.__k8s_api_listen_url_path %>"
    websocket_token_secret_file: "<%= config.gitlab_k8s_agent.__websocket_token_secret_file %>"
gitlab:
  address: "<%= config.gitlab_k8s_agent.__gitlab_address %>"
  external_url: "<%= config.gitlab_k8s_agent.__gitlab_external_url %>"
  authentication_secret_file: "<%= config.gitlab_k8s_agent.__secret_file %>"
  <%- if config.https? %>
  ca_certificate_file: "<%= gdk_root.join('localhost.crt') %>"
  <%- end %>
api:
  listen:
    network: "<%= config.gitlab_k8s_agent.internal_api_listen_network %>"
    address: "<%= config.gitlab_k8s_agent.internal_api_listen_address %>"
    authentication_secret_file: "<%= config.gitlab_k8s_agent.__secret_file %>"
redis:
  network: unix
  server:
    address: "<%= config.redis.__socket_file %>"
private_api:
  listen:
    network: "<%= config.gitlab_k8s_agent.private_api_listen_network %>"
    address: "<%= config.gitlab_k8s_agent.private_api_listen_address %>"
    authentication_secret_file: "<%= config.gitlab_k8s_agent.__private_api_secret_file %>"
observability:
  logging:
    level: debug
  <%- if config.gitlab_k8s_agent.otlp_endpoint != nil and config.gitlab_k8s_agent.otlp_endpoint.length > 1 %>
  tracing:
    otlp_endpoint: "<%= config.gitlab_k8s_agent.otlp_endpoint %>"
    otlp_token_secret_file: "<%= config.gitlab_k8s_agent.otlp_token_secret_file %>"
    otlp_ca_certificate_file: "<%= config.gitlab_k8s_agent.otlp_ca_certificate_file %>"
  <%- end %>
<%- if config.gitlab_k8s_agent.autoflow.enabled? %>
autoflow:
  http_client:
    allowed_ips: <%= config.gitlab_k8s_agent.autoflow.__http_client.allowed_ips %>
    allowed_ports: <%= config.gitlab_k8s_agent.autoflow.__http_client.allowed_ports %>
  temporal:
    host_port: '<%= config.gitlab_k8s_agent.autoflow.temporal.host_port %>'
    namespace: '<%= config.gitlab_k8s_agent.autoflow.temporal.namespace %>'
    <%- if config.gitlab_k8s_agent.autoflow.temporal.enable_tls %>
    enable_tls: true
    certificate_file: '<%= config.gitlab_k8s_agent.autoflow.temporal.certificate_file %>'
    key_file: '<%= config.gitlab_k8s_agent.autoflow.temporal.key_file %>'
    <%- end %>
    <%- if config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.enabled -%>
    workflow_data_encryption:
      secret_key_file: '<%= config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.__secret_key_file %>'
      codec_server:
        listen:
          network: '<%= config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.codec_server.listen.network %>'
          address: '<%= config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.codec_server.listen.address %>'
        temporal_web_ui_url: '<%= config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.codec_server.temporal_web_ui_url %>'
        temporal_oidc_url: '<%= config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.codec_server.temporal_oidc_url %>'
        authorized_user_emails: <%= config.gitlab_k8s_agent.autoflow.temporal.workflow_data_encryption.codec_server.authorized_user_emails %>
    <%- end %>
<%- end %>
<%- if config.workspaces.enabled? %>
workspaces:
  enabled: true
  listen:
    address: '<%= config.workspaces.listen.address %>'
    listen_grace_period: '<%= config.workspaces.listen.listen_grace_period %>'
    network: '<%= config.workspaces.listen.network %>'
    shutdown_grace_period: '<%= config.workspaces.listen.shutdown_grace_period %>'
<%- end %>
