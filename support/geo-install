#!/usr/bin/env bash

# This is the GDK + Geo one line installation. For more information, please visit:
# https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/index.md#one-line-installation
#
# Wrap everything in a function to ensure a partially downloaded install script
# is not executed. Inspired by https://install.sandstorm.io/
#
# Requires GITLAB_ACTIVATION_CODE or GITLAB_LICENSE_FILE to be set, like:
#
#   export GITLAB_ACTIVATION_CODE=your_activation_code
#
# Or:
#
#   export GITLAB_LICENSE_FILE="/path/to/license/file"
#
# Valid arguments are:
#
# 1 = directory in which to clone into, default is gdk (GDK_PRIMARY_INSTALL_DIR)
# 2 = directory in which to clone into, default is gdk2 (GDK_SECONDARY_INSTALL_DIR)
# 3 = git SHA/branch to checkout once cloned, default is main (GDK_CLONE_BRANCH)
# 4 = enable telemetry, default is false (ENABLE_TELEMETRY)
#
# Example usage with arguments:
#
#   curl "https://gitlab.com/gitlab-org/gitlab-development-kit/-/raw/main/support/geo-install" | bash -s gdk-a gdk-b my-gdk-branch-name true
#
_() {

set -eo pipefail

DEFAULT_GDK_PRIMARY_INSTALL_DIR="gdk"
DEFAULT_GDK_SECONDARY_INSTALL_DIR="gdk2"
DEFAULT_GDK_REPO_URL="https://gitlab.com/gitlab-org/gitlab-development-kit.git"
DEFAULT_GITLAB_REPO_URL="https://gitlab.com/gitlab-org/gitlab.git"

REQUIRED_COMMANDS=(git make)
REQUIRED_ANY_OF_ENV_VARS=(GITLAB_ACTIVATION_CODE GITLAB_LICENSE_FILE)

error() {
  echo "ERROR: ${1}" >&2
  exit 1
}

ensure_required_commands_exist() {
  for command in "${REQUIRED_COMMANDS[@]}"; do
    if ! command -v "${command}" > /dev/null 2>&1; then
     error "Please ensure ${command} is installed."
    fi
  done
}

# Function to check if any one of the list of environment variables is set
ensure_required_any_of_env_vars_exist() {
  local found=false
  for var_name in "${REQUIRED_ANY_OF_ENV_VARS[@]}"; do
    if [[ -n "${!var_name}" ]]; then
      found=true
      break
    fi
  done

  if ! $found; then
    echo "Support for GITLAB_LICENSE_KEY was removed in favor of standardized license variables."
    echo "Refer to doc/howto/geo.md for more info."
    error "Please ensure one of the following environment variables is set: ${REQUIRED_ANY_OF_ENV_VARS[*]}"
  fi
}

ensure_not_root() {
  if [[ ${EUID} -eq 0 ]]; then
    return 1
  fi

  return 0
}

clone_gdk_if_needed() {
  if [[ -d ${GDK_PRIMARY_INSTALL_DIR} ]]; then
    echo "INFO: A ${GDK_PRIMARY_INSTALL_DIR} directory already exists in the current working directory, resuming.."
  else
    git clone "${GDK_REPO_URL}" "${GDK_PRIMARY_INSTALL_DIR}"
  fi
}

clone_gdk2_if_needed() {
  if [[ -d ${GDK_SECONDARY_INSTALL_DIR} ]]; then
    echo "INFO: A ${GDK_SECONDARY_INSTALL_DIR} directory already exists in the current working directory, resuming.."
  else
    git clone "${GDK_PRIMARY_INSTALL_DIR}" "${GDK_SECONDARY_INSTALL_DIR}"
  fi
}

ensure_gdk_clone_branch_checked_out() {
  git -C "${PWD}/${GDK_PRIMARY_INSTALL_DIR}" fetch origin "${GDK_CLONE_BRANCH}"
  git -C "${PWD}/${GDK_PRIMARY_INSTALL_DIR}" checkout "${GDK_CLONE_BRANCH}"
}

setup_tool_version_manager() {
  local gdk_yml="${PWD}/gdk.yml"

  echo "INFO: Configuring mise as tool version manager."

  mkdir -p "$(dirname "${gdk_yml}")"

  cat << EOF > "${gdk_yml}"
---
tool_version_manager:
  enabled: true
EOF

  local full_path
  full_path=$(readlink -f "${gdk_yml}")

  echo "INFO: Tool version manager settings saved to ${full_path}:"
  cat "${gdk_yml}"
}

bootstrap() {
  make bootstrap
}

gdk_install() {
  ./support/tool-version-manager-exec . gdk install gitlab_repo="$GITLAB_REPO_URL" telemetry_enabled="$ENABLE_TELEMETRY"
}

add_license() {
  ./support/tool-version-manager-exec gitlab bundle install

  if ./support/tool-version-manager-exec gitlab bundle exec rake gitlab:license:info; then
    echo "INFO: Skipping license loading since it is already loaded."
    return
  fi

  ./support/tool-version-manager-exec gitlab bundle exec rake "gitlab:license:load[verbose]"
}

make_vite_default() {
  ./support/tool-version-manager-exec . gdk config set webpack.enabled false
  ./support/tool-version-manager-exec . gdk config set vite.enabled true
  ./support/tool-version-manager-exec . gdk reconfigure
  ./support/tool-version-manager-exec . gdk restart vite
}

echo
echo "INFO: This is the GDK + Geo one line installation. For more information, please visit:"
echo "INFO: https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/howto/geo.md#easy-installation"
echo "INFO:"
echo "INFO: The source for the installation script can be viewed at:"
echo "INFO: https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/support/geo-install"
echo

if [ $# -eq 1 ]; then
  echo "Where would you like to install the primary GDK? [./${DEFAULT_GDK_PRIMARY_INSTALL_DIR}]"
  read -r GDK_PRIMARY_INSTALL_DIR </dev/tty
  echo
  echo "Where would you like to install the secondary GDK? [./${DEFAULT_GDK_SECONDARY_INSTALL_DIR}]"
  read -r GDK_SECONDARY_INSTALL_DIR </dev/tty
  echo
  echo "Which GitLab repo URL would you like to clone? [${DEFAULT_GITLAB_REPO_URL}]"
  echo
  echo "ATTENTION: For members of the wider community, it is recommended to use the community fork (https://gitlab.com/gitlab-community/gitlab.git)."
  echo "See https://gitlab.com/gitlab-community/meta for instructions on how to join."
  echo "If you'd prefer to use your own repository, please ensure that its visibility is set to public."
  read -r GITLAB_REPO_URL </dev/tty
  echo
  echo "To improve GDK, GitLab would like to collect basic error and usage, including your platform and architecture."
  echo
  echo "Would you like to send telemetry anonymously to GitLab? [y/N]:"
  read -r consent </dev/tty
  case "$consent" in
    [yY]) ENABLE_TELEMETRY="true" ;;
    *)    ENABLE_TELEMETRY="false" ;;
  esac
else
  # Note: Passing arguments this way is meant for CI.
  # If you're running this manually, don't rely on the argument order as it may change.
  GDK_PRIMARY_INSTALL_DIR="${2-gdk}"
  GDK_SECONDARY_INSTALL_DIR="${3-gdk2}"
  GDK_CLONE_BRANCH="${4-main}"
  ENABLE_TELEMETRY="${5-false}"
fi

# Set defaults for any unset variables.
GDK_PRIMARY_INSTALL_DIR=${GDK_PRIMARY_INSTALL_DIR:-${DEFAULT_GDK_PRIMARY_INSTALL_DIR}}
GDK_SECONDARY_INSTALL_DIR=${GDK_SECONDARY_INSTALL_DIR:-${DEFAULT_GDK_SECONDARY_INSTALL_DIR}}
GDK_CLONE_BRANCH=${GDK_CLONE_BRANCH:-main}
GITLAB_REPO_URL=${GITLAB_REPO_URL:-${DEFAULT_GITLAB_REPO_URL}}
GDK_REPO_URL=${GDK_REPO_URL:-${DEFAULT_GDK_REPO_URL}}

if ! ensure_not_root; then
  error "Running as root is not supported."
fi

ensure_required_commands_exist
ensure_required_any_of_env_vars_exist

# Collapsible section for geo-install CI job. See https://docs.gitlab.com/ee/ci/jobs/index.html#custom-collapsible-sections
echo -e "\e[0Ksection_start:$(date +%s):set_up_primary_gdk\r\e[0KSet up primary GDK"
clone_gdk_if_needed
ensure_gdk_clone_branch_checked_out
cd "${GDK_PRIMARY_INSTALL_DIR}" || error "Clone of GDK should have created ${GDK_PRIMARY_INSTALL_DIR} directory."
setup_tool_version_manager
bootstrap
gdk_install
add_license

echo "then:"
echo "cd ${GDK_PRIMARY_INSTALL_DIR}"
echo
echo -e "\e[0Ksection_end:$(date +%s):set_up_primary_gdk\r\e[0K"

echo -e "\e[0Ksection_start:$(date +%s):set_up_secondary_gdk\r\e[0KSet up secondary GDK"
cd ..
clone_gdk2_if_needed
cd "${GDK_SECONDARY_INSTALL_DIR}" || error "Clone of GDK should have created ${GDK_SECONDARY_INSTALL_DIR} directory."
setup_tool_version_manager
cd "../${GDK_PRIMARY_INSTALL_DIR}" || error "Sanity check for ${GDK_PRIMARY_INSTALL_DIR} directory failed."
./support/tool-version-manager-exec . ./support/geo-add-secondary --secondary_port 3001 --primary . "../${GDK_SECONDARY_INSTALL_DIR}"

# Setup vite on the primary
make_vite_default
# CD into the secondary GDK directory and setup vite
cd "../${GDK_SECONDARY_INSTALL_DIR}"
make_vite_default

echo "INFO: To ensure you're in the newly installed secondary GDK directory, please run:"
echo
echo "cd ${GDK_SECONDARY_INSTALL_DIR}"
echo
echo -e "\e[0Ksection_end:$(date +%s):set_up_secondary_gdk\r\e[0K"
}

# If we've reached here, the entire install script has been downloaded and
# "should" be safe to execute.
_ "$0" "$@"
