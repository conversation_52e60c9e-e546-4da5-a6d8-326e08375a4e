workhorse_dir = ${gitlab_development_root}/gitlab/workhorse

.PHONY: **********************
**********************:
ifeq ($(workhorse_skip_setup),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Skipping gitlab-workhorse setup due to workhorse.skip_setup set to true"
	@echo "${DIVIDER}"
else
	$(Q)$(MAKE) **********************install gitlab/workhorse/gitlab-workhorse gitlab/workhorse/config.toml
endif

.PHONY: gitlab-workhorse-update
gitlab-workhorse-update: gitlab-workhorse-update-timed

.PHONY: gitlab-workhorse-update-run
gitlab-workhorse-update-run: **********************

.PHONY: **********************install
**********************install:
ifeq ($(tool_version_manager_enabled),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Installing tools from ${workhorse_dir}/.tool-versions"
	@echo "${DIVIDER}"
	$(Q)cd ${workhorse_dir} && $(MISE_INSTALL)
else
	@true
endif

.PHONY: **********************-bin
**********************-bin:
	$(Q)support/tool-version-manager-exec gitlab/workhorse $(MAKE) clean

.PHONY: gitlab/workhorse/gitlab-workhorse
gitlab/workhorse/gitlab-workhorse:
ifeq ($(**********************),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Downloading gitlab-workhorse binaries (workhorse.skip_compile set to true)"
	@echo "${DIVIDER}"
	$(Q)support/package-helper workhorse download
# WORKHORSE_TREE is needed in gitlab/tmp/tests/gitlab-workhorse so RSpec can detect the Workhorse version.
# We remove it from gitlab/workhorse to avoid it showing up as untracked, which would
# cause RSpec to rebuild GitLab Workhorse unnecessarily.
	$(Q)rm -f gitlab/workhorse/WORKHORSE_TREE
else
	$(Q)$(MAKE) **********************-bin
	@echo
	@echo "${DIVIDER}"
	@echo "Compiling gitlab/workhorse/gitlab-workhorse"
	@echo "${DIVIDER}"
	$(Q)support/tool-version-manager-exec gitlab/workhorse $(MAKE) ${QQ}
endif
