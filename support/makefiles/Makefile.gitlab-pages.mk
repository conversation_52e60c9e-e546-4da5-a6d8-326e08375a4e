gitlab_pages_dir = ${gitlab_development_root}/gitlab-pages

ifeq ($(gitlab_pages_enabled),true)
gitlab-pages-setup: gitlab-pages-update-timed
else
gitlab-pages-setup:
	@true
endif

ifeq ($(gitlab_pages_enabled),true)
gitlab-pages-update: gitlab-pages-update-timed
else
gitlab-pages-update:
	@true
endif

.PHONY: gitlab-pages-update-run
gitlab-pages-update-run: gitlab-pages-secret gitlab-pages/gitlab-pages.conf gitlab-pages/bin/gitlab-pages

gitlab-pages-tool-install:
ifeq ($(tool_version_manager_enabled),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Installing mise tools from ${gitlab_pages_dir}/.tool-versions"
	@echo "${DIVIDER}"
	$(Q)cd ${gitlab_pages_dir} && $(MISE_INSTALL)
else
	@true
endif

.PHONY: gitlab-pages/bin/gitlab-pages
gitlab-pages/bin/gitlab-pages: gitlab-pages/.git/pull gitlab-pages-tool-install
	@echo
	@echo "${DIVIDER}"
	@echo "Compiling gitlab-org/gitlab-pages"
	@echo "${DIVIDER}"
	$(Q)rm -f gitlab-pages/bin/gitlab-pages
	$(Q)support/tool-version-manager-exec ${gitlab_pages_dir} $(MAKE) ${QQ}

gitlab-pages/.git:
	$(Q)GIT_REVISION="${gitlab_pages_version}" support/component-git-clone ${git_params} ${gitlab_pages_repo} ${gitlab_pages_dir} ${QQ}

gitlab-pages/.git/pull: gitlab-pages/.git
	@echo
	@echo "${DIVIDER}"
	@echo "Updating gitlab-org/gitlab-pages to ${gitlab_pages_version}"
	@echo "${DIVIDER}"
	$(Q)support/component-git-update gitlab_pages "${gitlab_pages_dir}" "${gitlab_pages_version}" master
