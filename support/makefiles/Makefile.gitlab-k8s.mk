gitlab_k8s_agent_clone_dir = gitlab-k8s-agent

ifeq ($(gitlab_k8s_agent_enabled),true)
**********************: gitlab-k8s-agent/build/gdk/bin/kas_race gitlab-k8s-agent-config.yml gitlab-kas-websocket-token-secret gitlab-**********************workflow-data-encryption-secret
else
**********************:
	@true
endif

.PHONY: gitlab-k8s-agent-update
ifeq ($(gitlab_k8s_agent_enabled),true)
gitlab-k8s-agent-update: gitlab-k8s-agent-update-timed
else
gitlab-k8s-agent-update:
	@true
endif

.PHONY: gitlab-k8s-agent-update-run
gitlab-k8s-agent-update-run: ${gitlab_k8s_agent_clone_dir}/.git gitlab-k8s-agent/.git/pull gitlab-k8s-agent/build/gdk/bin/kas_race gitlab-k8s-agent-tool-install


.PHONY: gitlab-k8s-agent-clean
gitlab-k8s-agent-clean:
	$(Q)rm -rf "${gitlab_k8s_agent_clone_dir}/build/gdk/bin"
	cd "${gitlab_k8s_agent_clone_dir}" && bazel clean

gitlab-k8s-agent-tool-install:
ifeq ($(tool_version_manager_enabled),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Installing mise tools from ${gitlab_k8s_agent_clone_dir}"
	@echo "${DIVIDER}"
	$(Q)cd ${gitlab_k8s_agent_clone_dir} && $(MISE_INSTALL)
else
	@true
endif

gitlab-k8s-agent/build/gdk/bin/kas_race: ${gitlab_k8s_agent_clone_dir}/.git
	@echo
	@echo "${DIVIDER}"
	@echo "Installing gitlab-org/cluster-integration/gitlab-agent"
	@echo "${DIVIDER}"
	$(Q)mkdir -p "${gitlab_k8s_agent_clone_dir}/build/gdk/bin"
	$(Q)support/tool-version-manager-exec "${gitlab_k8s_agent_clone_dir}" $(MAKE) gdk-install TARGET_DIRECTORY="$(CURDIR)/${gitlab_k8s_agent_clone_dir}/build/gdk/bin" ${QQ}

${gitlab_k8s_agent_clone_dir}/.git:
	$(Q)GIT_REVISION="${gitlab_k8s_agent_version}" support/component-git-clone ${git_params} ${gitlab_k8s_agent_repo} ${gitlab_k8s_agent_clone_dir} ${QQ}

gitlab-k8s-agent/.git/pull:
	@echo
	@echo "${DIVIDER}"
	@echo "Updating gitlab-org/cluster-integration/gitlab-agent to ${gitlab_k8s_agent_version}"
	@echo "${DIVIDER}"
	$(Q)support/component-git-update gitlab_k8s_agent "${gitlab_k8s_agent_clone_dir}" "${gitlab_k8s_agent_version}" master
