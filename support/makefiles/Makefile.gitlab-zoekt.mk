gitlab_zoekt_indexer_dir = ${gitlab_development_root}/gitlab-zoekt-indexer

.PHONY: gitlab-zoekt-setup
ifeq ($(zoekt_enabled),true)
gitlab-zoekt-setup: gitlab-zoekt-indexer/.git/pull gitlab-zoekt-indexer/bin/gitlab-zoekt
else
gitlab-zoekt-setup:
	@true
endif

.PHONY: gitlab-zoekt-update
ifeq ($(zoekt_enabled),true)
gitlab-zoekt-update: gitlab-zoekt-update-timed
else
gitlab-zoekt-update:
	@true
endif

.PHONY: gitlab-zoekt-update-run
gitlab-zoekt-run: gitlab-zoekt-indexer/.git/pull gitlab-zoekt-indexer-clean-bin gitlab-zoekt-indexer/bin/gitlab-zoekt-indexer

gitlab-zoekt-tool-install:
ifeq ($(tool_version_manager_enabled),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Installing mise tools from ${gitlab_zoekt_indexer_dir}/.tool-versions"
	@echo "${DIVIDER}"
	$(Q)cd ${gitlab_zoekt_indexer_dir} && $(MISE_INSTALL)
else
	@true
endif

gitlab-zoekt-clean-bin:
	$(Q)rm -rf gitlab-zoekt-indexer/bin/*

gitlab-zoekt-indexer/.git:
	$(Q)GIT_REVISION="${gitlab_zoekt_indexer_version}" support/component-git-clone ${git_params} ${gitlab_zoekt_indexer_repo} gitlab-zoekt-indexer

.PHONY: gitlab-zoekt-indexer/bin/gitlab-zoekt
gitlab-zoekt-indexer/bin/gitlab-zoekt: gitlab-zoekt-tool-install
	@echo
	@echo "${DIVIDER}"
	@echo "Building gitlab-org/gitlab-zoekt version ${gitlab_zoekt_indexer_version}"
	@echo "${DIVIDER}"
	$(Q)support/tool-version-manager-exec gitlab-zoekt-indexer $(MAKE) build_unified ${QQ}

.PHONY: gitlab-zoekt-indexer/.git/pull
gitlab-zoekt-indexer/.git/pull: gitlab-zoekt-indexer/.git
	@echo
	@echo "${DIVIDER}"
	@echo "Updating gitlab-org/gitlab-zoekt-indexer"
	@echo "${DIVIDER}"
	$(Q)support/component-git-update zoekt gitlab-zoekt-indexer "${gitlab_zoekt_indexer_version}" main
