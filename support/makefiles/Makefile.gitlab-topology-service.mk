gitlab_topology_service_dir = ${gitlab_development_root}/gitlab-topology-service

.PHONY: gitlab-**********************
ifeq ($(gitlab_topology_service_enabled),true)
gitlab-**********************: gitlab-**********************-timed gitlab-topology-service/config.toml
else
gitlab-**********************:
	@true
endif

.PHONY: gitlab-**********************-run
gitlab-**********************-run: gitlab-topology-service/.git gitlab-topology-service-common-setup
	$(Q)gdk restart gitlab-topology-service

gitlab-topology-service/.git:
	$(Q)rm -fr gitlab-topology-service/config.toml
	$(Q)support/component-git-clone ${git_params} ${gitlab_topology_service_repo} gitlab-topology-service
	$(Q)support/component-git-update gitlab_topology_service gitlab-topology-service "${gitlab_topology_service_version}" main

.PHONY: gitlab-topology-service-common-setup
gitlab-topology-service-common-setup: touch-examples gitlab-topology-service/config.toml gitlab-**********************deps-install

.PHONY: gitlab-**********************deps-install
gitlab-**********************deps-install: gitlab-topology-service-tool-install
	@echo
	@echo "${DIVIDER}"
	@echo "Performing make deps steps for ${gitlab_topology_service_dir}"
	@echo "${DIVIDER}"
	$(Q)support/tool-version-manager-exec ${gitlab_topology_service_dir} make deps

.PHONY: gitlab-topology-service-tool-install
gitlab-topology-service-tool-install:
ifeq ($(tool_version_manager_enabled),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Installing mise tools from ${gitlab_topology_service_dir}/.tool-versions"
	@echo "${DIVIDER}"
	$(Q)cd ${gitlab_topology_service_dir} && $(MISE_INSTALL)
else
	@true
endif

.PHONY: gitlab-topology-service-update
ifeq ($(gitlab_topology_service_enabled),true)
gitlab-topology-service-update: gitlab-topology-service-update-timed
else
gitlab-topology-service-update:
	@true
endif

.PHONY: gitlab-topology-service-update-run
gitlab-topology-service-update-run: gitlab-topology-service/.git/pull gitlab-topology-service-common-setup gitlab-topology-service/config.toml
	$(Q)gdk restart gitlab-topology-service

.PHONY: gitlab-topology-service/.git/pull
gitlab-topology-service/.git/pull: gitlab-topology-service/.git
	@echo
	@echo "${DIVIDER}"
	@echo "Updating ${gitlab_topology_service_dir}"
	@echo "${DIVIDER}"
	$(Q)support/component-git-update gitlab_topology_service gitlab-topology-service "${gitlab_topology_service_version}" main
