psql := $(postgresql_bin_dir)/psql

.PHONY: postgresql-replica-setup
ifeq ($(postgresql_replica_enabled),true)
postgresql-replica-setup: **********************/config postgresql-replica/data
else
postgresql-replica-setup:
	@true
endif

.PHONY: ********************-setup
ifeq ($(postgresql_replica_enabled2),true)
********************-setup: **********************/config ********************/data
else
********************-setup:
	@true
endif

postgresql-replica/data:
	$(Q)support/tool-version-manager-exec . pg_basebackup -R -h ${postgresql_dir} -D ${postgresql_replica_data_dir} -P -U gitlab_replication --wal-method=fetch

********************/data:
	$(Q)support/tool-version-manager-exec . pg_basebackup -R -h ${postgresql_dir} -D ${postgresql_replica_data_dir2} -P -U gitlab_replication --wal-method=fetch

**********************-primary-create-slot: **********************/slot

**********************/backup:
	$(Q)gdk rake geo:replication_backup
	$(Q)$(MAKE) ${postgresql_data_dir}/gitlab.conf ${QQ}

**********************/slot:
	$(Q)$(psql) -h ${postgresql_host} -p ${postgresql_port} -d postgres -c "SELECT * FROM pg_create_physical_replication_slot('gitlab_gdk_replication_slot');"

**********************/list-slots:
	$(Q)$(psql) -h ${postgresql_host} -p ${postgresql_port} -d postgres -c "SELECT * FROM pg_replication_slots;"

**********************/drop-slot:
	$(Q)$(psql) -h ${postgresql_host} -p ${postgresql_port} -d postgres -c "SELECT pg_drop_replication_slot('gitlab_gdk_replication_slot');" 2>/dev/null || echo "Replication slot 'gitlab_gdk_replication_slot' does not exist"

**********************/config:
	$(Q)support/tool-version-manager-exec . ./support/postgres-replication
