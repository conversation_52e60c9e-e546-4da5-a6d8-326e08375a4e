gitlab_dir = ${gitlab_development_root}/gitlab
gitlab_rake_cmd = support/tool-version-manager-exec gitlab bundle exec rake
gitlab_git_cmd = git -C $(gitlab_dir)
default_branch ?= $(if $(gitlab_default_branch),$(gitlab_default_branch),master)

.PHONY: gitlab-setup
gitlab-setup: gitlab/.git gitlab-config gitlab-tool-install .gitlab-bundle .gitlab-lefthook .gitlab-yarn .gitlab-translations

gitlab/doc/api/graphql/reference/gitlab_schema.json: .gitlab-bundle
	@echo
	@echo "${DIVIDER}"
	@echo "Generating gitlab GraphQL schema files"
	@echo "${DIVIDER}"
	$(Q)$(gitlab_rake_cmd) gitlab:graphql:schema:dump ${QQ}

gitlab/.git:
	@echo
	@echo "${DIVIDER}"
	@echo "Cloning ${gitlab_repo}"
	@echo "${DIVIDER}"
	$(Q)support/component-git-clone ${git_params} $(if $(realpath ${gitlab_repo}),--shared) ${gitlab_repo} ${gitlab_dir}

gitlab-tool-install:
ifeq ($(tool_version_manager_enabled),true)
	@echo
	@echo "${DIVIDER}"
	@echo "Installing mise tools from ${gitlab_dir}/.tool-versions"
	@echo "${DIVIDER}"
	$(Q)cd ${gitlab_dir} && $(MISE_INSTALL)
else
	@true
endif

gitlab-config: touch-examples gitlab/public/uploads
	$(Q)rake \
		gitlab/config/gitlab.yml \
		gitlab/config/database.yml \
		gitlab/config/cable.yml \
		gitlab/config/resque.yml \
		gitlab/config/redis.cache.yml \
		gitlab/config/redis.repository_cache.yml \
		gitlab/config/redis.queues.yml \
		gitlab/config/redis.shared_state.yml \
		gitlab/config/redis.trace_chunks.yml \
		gitlab/config/redis.rate_limiting.yml \
		gitlab/config/redis.sessions.yml \
		gitlab/config/vite.gdk.json \
		gitlab/config/puma.rb

gitlab/public/uploads:
	$(Q)mkdir $@

.PHONY: gitlab-bundle-prepare
gitlab-bundle-prepare:
	@echo
	@echo "${DIVIDER}"
	@echo "Setting up Ruby bundler"
	@echo "${DIVIDER}"
	${Q}. ./support/bootstrap-common.sh ; configure_ruby_bundler_for_gitlab

.gitlab-bundle: gitlab-bundle-prepare
	@echo
	@echo "${DIVIDER}"
	@echo "Installing gitlab-org/gitlab Ruby gems"
	@echo "${DIVIDER}"
	${Q}$(support_bundle_install) $(gitlab_dir)
	$(Q)touch $@

ifeq ($(gitlab_lefthook_enabled),true)
.gitlab-lefthook:
	@echo
	@echo "${DIVIDER}"
	@echo "Enabling Lefthook for gitlab-org/gitlab"
	@echo "${DIVIDER}"
	$(Q)support/tool-version-manager-exec gitlab bundle exec lefthook install
	$(Q)touch $@
else
.gitlab-lefthook:
	@true
endif

.gitlab-yarn:
	@echo
	@echo "${DIVIDER}"
	@echo "Installing gitlab-org/gitlab Node.js packages"
	@echo "${DIVIDER}"
ifeq ($(vite_enabled),true)
	$(Q)echo "Deleting Vite cache"
	$(Q)rm -rf gitlab/tmp/cache/vite || echo "Warning: Failed to delete Vite cache directory"
endif
	$(Q)support/tool-version-manager-exec gitlab yarn install --pure-lockfile
	$(Q)touch $@

.PHONY: gitlab-translations-unlock
gitlab-translations-unlock:
	$(Q)rm -f .gitlab-translations

.PHONY: gitlab-translations
gitlab-translations: gitlab-translations-timed

.PHONY: gitlab-translations-run
gitlab-translations-run: .gitlab-translations

.gitlab-translations:
	@echo
	@echo "${DIVIDER}"
	@echo "Generating gitlab-org/gitlab Rails translations"
	@echo "${DIVIDER}"
	$(Q)rake gitlab:**********************
	$(Q)$(gitlab_git_cmd) checkout locale/*/gitlab.po
	$(Q)touch $@
