#!/usr/bin/env bash

set -euo pipefail

parent_path=$(dirname "$0")

# shellcheck source=support/bootstrap-common.sh
source "${parent_path}"/bootstrap-common.sh

mise_update() {
  header_print "Updating mise plugins"
  if ! mise_update_plugins; then
    error "Failed to update some mise plugins." >&2
  fi
}

###############################################################################

if ! common_preflight_checks; then
  error "Failed to perform preflight checks." >&2
fi

header_print "Ensuring platform software installed"
if ! setup_platform; then
  error "Failed to install platform software." >&2
fi

if ! ensure_gdk_in_default_gems; then
  error "Failed to ensure gdk is in default gems." >&2
fi

if mise_enabled; then
  mise_update
fi

if ! gdk_install_gdk_clt; then
  error "Failed to run gdk_install_gdk_clt()." >&2
fi

if ! configure_ruby; then
  error "Failed to configure Ruby." >&2
fi
