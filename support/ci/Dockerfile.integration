# shellcheck disable=SC2086,SC2154 shell=bash
ARG from_image
FROM ${from_image}

ARG GITLAB_CI_CACHE_DIR
ARG GITLAB_CI_CACHE_GO_DIR
ARG SHA
ARG REPO_URL
ARG GDK_DEBUG

RUN cd /home/<USER>/gdk && git remote add integration "$REPO_URL" && git fetch integration "$SHA" && git checkout "$SHA"
RUN cd /home/<USER>/gdk && \
    GDK_SELF_UPDATE=0 gdk update && \
    (cd /home/<USER>/gdk && GDK_KILL_CONFIRM=true gdk kill) && \
    (du -smx "${GITLAB_CI_CACHE_DIR}"/* || true) && \
    (sudo rm -rf "${GITLAB_CI_CACHE_GO_DIR}" || true) && \
    (du -smx "${GITLAB_CI_CACHE_DIR}"/* || true) && \
    (du -smx /home/<USER>/.cache/* || true) && \
    (sudo rm -rf /home/<USER>/.cache/yarn || true) && \
    (du -smx /home/<USER>/.cache/* || true)
