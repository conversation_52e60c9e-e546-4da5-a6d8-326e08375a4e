# shellcheck disable=SC2086,SC2154 shell=bash
FROM archlinux:base-20250413.0.335299

ARG BRANCH=main

RUN export LC_ALL=en_US.UTF-8
RUN export LANG=en_US.UTF-8
RUN export LANGUAGE=en_US.UTF-8

RUN pacman -Syu curl git make sudo --noconfirm

RUN useradd --user-group --create-home --groups wheel --shell /bin/bash gdk
RUN echo "gdk ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/gdk_no_password

USER gdk
WORKDIR /home/<USER>

SHELL ["/bin/bash", "-i", "-c"]

RUN curl --fail "https://gitlab.com/gitlab-org/gitlab-development-kit/-/raw/${BRANCH}/support/install" | bash -s - gdk "${BRANCH}" "true"
