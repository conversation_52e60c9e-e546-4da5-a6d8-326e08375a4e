#!/usr/bin/env bash

set -eo pipefail

PROJECT=$1

install_common_deps() {
  echo "Installing common dependencies..."
  gem install gitlab-sdk sentry-ruby zeitwerk tty-spinner
}

install_macos_deps() {
  echo "Installing macOS dependencies..."
  brew install icu4c libyaml mise

  MISE_PREFIX=$(brew --prefix mise)
  export PATH="$MISE_PREFIX/bin:$PATH"
  eval "$(mise activate bash)"
}

install_linux_deps() {
  echo "Installing Linux dependencies..."
  apt-get update
  apt-get install -y software-properties-common
  add-apt-repository ppa:git-core/ppa -y
  apt-get update
  apt-get install -y git

  export PATH="/usr/bin:$PATH"

  echo "Verifying Git version..."
  git --version
  echo "Git location: $(which git)"
}

install_gitaly_deps() {
  if [[ "$BUILD_OS" == "linux" ]]; then
    echo "Installing Gitaly-specific dependencies..."
    apt-get install -y libcurl4-openssl-dev libpcre2-dev libssl-dev libz-dev meson ninja-build
  fi
}

install_os_deps() {
  if [[ "$BUILD_OS" == "darwin" ]]; then
    install_macos_deps
  elif [[ "$BUILD_OS" == "linux" ]]; then
    install_linux_deps
  fi

  if [[ "$PROJECT" == "gitaly" ]]; then
    install_gitaly_deps
  fi
}

clone_repository() {
  local repo_url=$1
  local branch=$2
  local work_dir=$3
  local depth=${4:-1}

  echo "Cloning $repo_url..."

  local clone_args=("--branch" "$branch" "--single-branch")

  if [ "$depth" != "full" ]; then
    clone_args+=("--depth" "$depth")
  fi

  git clone "${clone_args[@]}" "$repo_url"
  cd "$work_dir" || exit 1
}

setup_build_environment() {
  echo "Creating directories:" "$@"
  mkdir -p -- "$@"

  if [[ "$BUILD_OS" == "darwin" ]]; then
    echo "Installing golang from .tool-versions..."
    local golang_version
    golang_version=$(awk '/^golang/ {print $2}' .tool-versions)
    mise install "golang@${golang_version}" --silent
  fi
}

fetch_version() {
  local project=$1
  local url=$2

  echo "Fetching version for $project..."

  local version
  version=$(curl --silent --fail --url "$url")

  if [[ -z "$version" ]]; then
    echo "Error: Could not fetch version for $project from $url"
    exit 1
  fi

  export PACKAGE_VERSION="$version"
}

generate_checksums() {
  echo "Generating checksums..."
  find "$1" -type f -print0 | xargs -0 sha256sum > "$1/checksums.txt"
}

generate_metadata() {
  local output="$1/metadata.txt"

  echo "Generating metadata..."

  {
    echo "Timestamp: $(date)"
    echo "uname: $(uname -a)"
    echo "CI_JOB_STARTED_AT: ${CI_JOB_STARTED_AT}"
    echo "CI_COMMIT_SHA: ${CI_COMMIT_SHA}"
    echo "CI_JOB_ID: ${CI_JOB_ID}"
    echo "CI_JOB_URL: ${CI_JOB_URL}"
    echo "BUILD_ARCH: ${BUILD_ARCH}"
    echo "BUILD_OS: ${BUILD_OS}"
  } >> "$output"
}

compile_gitaly() {
  fetch_version "gitaly" "$GITALY_SERVER_VERSION_FILE_URL"
  clone_repository "https://gitlab.com/gitlab-org/gitaly.git" "master" "gitaly" full
  setup_build_environment "build"

  if [[ "$BUILD_OS" == "darwin" ]]; then
    local meson_version ninja_version
    meson_version=$(awk '/^meson/ {print $2}' .tool-versions)
    ninja_version=$(awk '/^ninja/ {print $2}' .tool-versions)
    mise install "meson@${meson_version}" --silent
    mise install "ninja@${ninja_version}" --silent
  fi

  echo "Building Gitaly..."
  git checkout "$PACKAGE_VERSION"
  if command -v mise >/dev/null 2>&1; then
    GOOS=$BUILD_OS GOARCH=$BUILD_ARCH mise exec -- make WITH_BUNDLED_GIT=YesPlease BUNDLE_FLAGS=--no-deployment USE_MESON=YesPlease
  else
    GOOS=$BUILD_OS GOARCH=$BUILD_ARCH make WITH_BUNDLED_GIT=YesPlease BUNDLE_FLAGS=--no-deployment USE_MESON=YesPlease
  fi

  echo "Cleaning up unnecessary binaries..."
  rm _build/bin/gitaly-git*
  rm _build/bin/gitaly-{ssh,hooks,lfs-smudge,gpg}

  echo "Moving binaries to build directory..."
  mv _build/bin/* build

  generate_metadata build
  generate_checksums build

  cd "$CI_PROJECT_DIR" || exit 1
  support/package-helper gitaly upload
}

********************() {
  fetch_version "gitlab-shell" "$GITLAB_SHELL_VERSION_FILE_URL"
  clone_repository "https://gitlab.com/gitlab-org/gitlab-shell.git" "v$PACKAGE_VERSION" "gitlab-shell"
  setup_build_environment "build"

  echo "Building GitLab Shell..."
  if command -v mise >/dev/null 2>&1; then
    CGO_ENABLED=0 GOOS=$BUILD_OS GOARCH=$BUILD_ARCH mise exec -- make build
  else
    CGO_ENABLED=0 GOOS=$BUILD_OS GOARCH=$BUILD_ARCH make build
  fi

  echo "Moving binaries to build directory..."
  mv bin/* build

  generate_metadata build
  generate_checksums build

  cd "$CI_PROJECT_DIR" || exit 1
  support/package-helper gitlab_shell upload
}

compile_workhorse() {
  fetch_version "workhorse" "$GITLAB_WORKHORSE_VERSION_FILE_URL"
  clone_repository "https://gitlab.com/gitlab-org/gitlab.git" "master" "gitlab/workhorse"
  setup_build_environment "build"

  echo "Building GitLab Workhorse..."
  git checkout "$PACKAGE_VERSION"
  if command -v mise >/dev/null 2>&1; then
    GOOS=$BUILD_OS GOARCH=$BUILD_ARCH mise exec -- make
  else
    GOOS=$BUILD_OS GOARCH=$BUILD_ARCH make
  fi

  echo "Moving binaries to build directory..."
  mv gitlab-* build

  echo "Writing WORKHORSE_TREE file..."
  git rev-parse HEAD:workhorse > build/WORKHORSE_TREE

  generate_metadata build
  generate_checksums build

  cd "$CI_PROJECT_DIR" || exit 1
  support/package-helper workhorse upload
}

compile_openbao() {
  fetch_version "openbao" "$GITLAB_OPENBAO_VERSION_FILE_URL"
  clone_repository "https://gitlab.com/gitlab-org/govern/secrets-management/openbao-internal.git" "main" "openbao-internal" full
  echo "golang 1.24.0" > .tool-versions
  setup_build_environment "bin"

  echo "Building Openbao..."
  git checkout "$PACKAGE_VERSION"
  git submodule init
  git submodule update
  if command -v mise >/dev/null 2>&1; then
    mise exec -- make clean build
  else
    make clean build
  fi

  generate_metadata bin
  generate_checksums bin

  cd "$CI_PROJECT_DIR" || exit 1
  support/package-helper openbao upload
}

main() {
  required_vars=(PROJECT BUILD_OS BUILD_ARCH)

  for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
      echo "Error: $var environment variable is not set"
      exit 1
    fi
  done

  install_os_deps
  install_common_deps

  case "$PROJECT" in
    gitaly)
      compile_gitaly
      ;;
    gitlab-shell)
      ********************
      ;;
    openbao)
      compile_openbao
      ;;
    workhorse)
      compile_workhorse
      ;;
    *)
      echo "Usage: $0 [gitaly|gitlab-shell|openbao|workhorse]"
      exit 1
      ;;
  esac
}

main "$@"
