#!/usr/bin/env bash

replication_include="include 'replication.conf'"
postgresql_dir=$(gdk config get postgresql.dir 2>/dev/null)
data_dir=$(gdk config get postgresql.data_dir 2>/dev/null)
postgres_version=$(gdk config get postgresql.active_version 2>/dev/null | cut -d. -f1)
postgresql_replication_user=$(gdk config get postgresql.replication_user 2>/dev/null)
postgresql_port=$(gdk config get postgresql.port 2>/dev/null)


# Check if the replication role already exists and create it if it does not
psql -t -h "$postgresql_dir" -p "$postgresql_port" -d postgres -c "SELECT 1 FROM pg_user WHERE usename = '$postgresql_replication_user'" \
 | grep -q 1 \
 || psql -h "$postgresql_dir" -p "$postgresql_port" -d postgres -c "CREATE ROLE $postgresql_replication_user WITH REPLICATION LOGIN;"

if [[ $postgres_version -ge 13 ]]; then
    wal_keep_setting_name="wal_keep_size"
    wal_keep_setting_value="200"
else
    wal_keep_setting_name="wal_keep_segments"
    wal_keep_setting_value="10"
fi

# Append replication include if it's not already in the postgresql.conf
if ! grep -Fxq "$replication_include" "$data_dir"/postgresql.conf; then
  echo "$replication_include" >> "$data_dir"/postgresql.conf
fi

# Create replication.conf if it does not already exist
if [ ! -f "$data_dir/replication.conf" ]; then
  cat <<EOF > "$data_dir"/replication.conf
wal_level = hot_standby
max_replication_slots = 2
max_wal_senders = 10
${wal_keep_setting_name} = ${wal_keep_setting_value}
hot_standby = on
EOF
fi
