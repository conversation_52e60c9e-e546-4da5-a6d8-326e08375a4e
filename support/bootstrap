#!/usr/bin/env bash

set -euo pipefail

parent_path=$(dirname "$0")

# shellcheck source=support/bootstrap-common.sh
source "${parent_path}"/bootstrap-common.sh

GDK_BOOTSTRAPPED_FILE="${GDK_CACHE_DIR}/.gdk_bootstrapped"

add_tool_initializer() {
  local shell_file="$1"
  local init_command="$2"

  if [[ "${shell_file}" == "${HOME}/.zshrc" && ! -f "${shell_file}" ]]; then
    shell_file="${HOME}/.zprofile"
  fi

  [[ -f "${shell_file}" ]] && {
    if ! grep -Fq "${init_command}" "${shell_file}"; then
      echo -e "\n# Added by GDK bootstrap\n${init_command}" >> "${shell_file}"
    fi
  }

  return 0
}

mise_install() {
  # Resolve gpg verification error in CI
  if [[ "${CI:-}" == "true" ]] && [[ "${OSTYPE}" == "darwin"* ]]; then
    if [[ -d ~/.gnupg ]]; then
      mv ~/.gnupg ~/.gnupg-backup
    fi
    mkdir ~/.gnupg
    chmod 700 ~/.gnupg
  fi

  if [[ "${OSTYPE}" == "darwin"* ]] && ! command -v mise &> /dev/null; then
    error "mise is not installed. Run the following commands:
      rm ${ROOT_PATH}/.cache/.gdk_platform_setup ${ROOT_PATH}/.cache/.gdk_bootstrapped\n
      make bootstrap"
  elif ! [[ -x "$MISE_INSTALL_PATH" ]]; then
    header_print "Installing mise..."
    curl -fsSL https://mise.run | MISE_VERSION=$(< "${ROOT_PATH}/.mise-version") bash
  fi

  # For more information on setting up mise in different shells, see https://mise.jdx.dev/installing-mise.html#shells.
  add_tool_initializer "${HOME}/.bashrc" "eval \"\$(${MISE_INSTALL_PATH} activate bash)\""
  add_tool_initializer "${HOME}/.zshrc" "eval \"\$(${MISE_INSTALL_PATH} activate zsh)\""
  add_tool_initializer "${HOME}/.config/fish/config.fish" "${MISE_INSTALL_PATH} activate fish | source"
  add_tool_initializer "${HOME}/.config/elvish/rc.elv" "var mise: = (ns [&])\neval (\$(${MISE_INSTALL_PATH} activate elvish | slurp)) &ns=\$mise: &on-end={|ns| set mise: = \$ns }\nmise:activate"
  add_tool_initializer "${HOME}/.config/nushell/env.nu" "let mise_path = \$nu.default-config-dir | path join mise.nu\n^mise activate nu | save \$mise_path --force"
  add_tool_initializer "${HOME}/.config/nushell/config.nu" "use (\$nu.default-config-dir | path join mise.nu)"

  return 0
}

mise_activate() {
  if [[ ! "$PATH" =~ "mise/shims" ]] && [[ -z "${CI:-}" ]]; then
    # We need to enable shims here so mise picks up new tools as they're added,
    # since this script is running non-interactively.
    # https://mise.jdx.dev/dev-tools/shims.html#shims-vs-path
    eval "$($MISE_INSTALL_PATH activate bash --shims)"
  fi

  "${MISE_INSTALL_PATH}" trust "${ROOT_PATH}/mise.toml"
}

mise_install_tools() {
  header_print "Installing mise tools..."

  # Install Rust before Ruby to ensure YJIT is available.
  echo "INFO: Installing Rust before Ruby..."
  export RUST_WITHOUT=rust-docs
  grep -E '^rust' .tool-versions | awk '{ print $1 " " $2 }' | xargs -I {} sh -c "mise install {}"
  rust_version=$(grep -E '^rust' .tool-versions | awk '{ print $2 }')

  # Ruby attempts to find a rustc in the PATH, but the version has to be
  # set for mise to use it. Setting the version in the
  # environment avoids the need to set this in ~/.tool-versions.
  echo "INFO: Setting MISE_RUST_VERSION to ${rust_version}"
  export MISE_RUST_VERSION=${rust_version}

  # Install just one Ruby version to run the rake task
  echo "INFO: Installing initial Ruby version..."
  ruby_version=$(grep -E '^ruby' .tool-versions | awk '{ print $2 }' | head -n1)
  mise install ruby "${ruby_version}"

  # Install Node.js first for later installation of Node.js-based dependencies (e.g., markdownlint-cli2)
  echo "INFO: Installing initial Node.js version..."
  nodejs_version=$(grep -E '^nodejs' .tool-versions | awk '{ print $2 }' | head -n1)
  mise install nodejs "${nodejs_version}"

  echo "INFO: Installing remaining tools..."
  bundle install --quiet && bundle exec rake update:tool-versions

  return $?
}

set_mise_paths() {
  local mise_detected_path
  mise_detected_path=$(type -p mise | cut -d' ' -f3 2>/dev/null) || true

  MISE_INSTALL_PATH="${MISE_INSTALL_PATH:-$HOME/.local/bin/mise}"
  MISE_DATA_DIR="${MISE_DATA_DIR:-$HOME/.local/share/mise}"

  if [[ -x "$mise_detected_path" ]]; then
    MISE_INSTALL_PATH="$mise_detected_path"
  elif [[ "${OSTYPE}" == "darwin"* ]] && [[ -x "$(brew --prefix 2>/dev/null)/bin/mise" ]]; then
    MISE_INSTALL_PATH="$(brew --prefix)/bin/mise"
  fi
}

gdk_mark_bootstrapped() {
  mkdir -p "${GDK_CACHE_DIR}"
  touch "${GDK_BOOTSTRAPPED_FILE}"

  echo
  info "Bootstrap successful!"
}

###############################################################################

if [[ -f "${GDK_BOOTSTRAPPED_FILE}" ]]; then
  info "This GDK has already been bootstrapped."
  info "Remove '${GDK_BOOTSTRAPPED_FILE}' to re-bootstrap."
  exit 0
fi

if ! common_preflight_checks; then
  error "Failed to perform preflight checks." >&2
fi

if ! setup_platform; then
  error "Failed to install set up platform." >&2
fi

if tool_version_manager_enabled; then
  set_mise_paths

  if ! mise_install; then
    error "Failed to install mise." >&2
  fi

  mise_activate

  if ! ensure_gdk_in_default_gems; then
    error "Failed to ensure gdk is in default gems." >&2
  fi

  if ! mise_install_tools; then
    error "Failed to install some mise tools." >&2
  fi
fi

if ! gdk_install_gdk_clt; then
  error "Failed to run gdk_install_gdk_clt()." >&2
fi

if ! configure_ruby; then
  error "Failed to configure Ruby." >&2
fi

gdk_mark_bootstrapped
