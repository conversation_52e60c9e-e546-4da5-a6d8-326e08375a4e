#!/usr/bin/env ruby
# frozen_string_literal: true

require_relative '../lib/gdk'

abort "usage: #{File.basename(__FILE__)} COMPONENT WORKTREE REVISION DEFAULT_BRANCH" if ARGV.count < 4

component_name, worktree, revision, default_branch = ARGV

begin
  exit(1) unless GDK::Project::Base.new(component_name, GDK.config.gdk_root.join(worktree), default_branch).update(revision)
rescue StandardError => e
  GDK::Output.abort(e)
end
