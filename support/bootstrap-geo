#!/usr/bin/env ruby
#
# frozen_string_literal: true

success = true

success &&= system(*%w[gdk start postgresql-geo])

# Create gitlabhq_geo_development. This command is not idempotent.
system({ 'force' => 'yes', 'BOOTSTRAP' => '1', 'RAILS_ENV' => 'development' }, *%w[../support/tool-version-manager-exec . bundle exec rails db:create:geo], chdir: 'gitlab')

# Run migrations on gitlabhq_geo_development.
success &&= system({ 'force' => 'yes', 'BOOTSTRAP' => '1', 'RAILS_ENV' => 'development' }, *%w[../support/tool-version-manager-exec . bundle exec rails db:migrate:geo], chdir: 'gitlab')

# Create gitlabhq_geo_test. This command is not idempotent, but it's also not a huge deal if it fails.
system({ 'force' => 'yes', 'BOOTSTRAP' => '1', 'RAILS_ENV' => 'test' }, *%w[../support/tool-version-manager-exec . bundle exec rails db:create:geo], chdir: 'gitlab')

# To recreate the Praefect DB
success &&= system(*%w[gdk reconfigure], chdir: 'gitlab')

exit if success

abort "#{$PROGRAM_NAME} failed"
