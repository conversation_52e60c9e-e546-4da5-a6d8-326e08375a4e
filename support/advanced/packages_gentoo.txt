# Keep the file list alphabetically sorted, makes diffs and merges easier
#
app-admin/sudo
app-arch/unzip
app-crypt/gnupg
app-crypt/mit-krb5
app-portage/elt-patches
dev-build/meson
dev-build/ninja
dev-db/sqlite
dev-libs/icu
dev-libs/jemalloc
dev-libs/libffi
dev-libs/libpcre2
dev-libs/libxml2
dev-libs/libxslt
dev-libs/libyaml
dev-libs/openssl
dev-libs/re2
dev-python/docutils
dev-util/cmake
dev-util/pkgconf
dev-util/yamllint
dev-vcs/git
dev-vcs/git-lfs
media-gfx/graphicsmagick
media-libs/exiftool
net-misc/curl
net-misc/wget
sys-apps/ed
sys-apps/less
sys-apps/net-tools
sys-apps/util-linux # contains uuid
sys-apps/which
sys-devel/autoconf
sys-devel/automake
sys-devel/bison
sys-devel/flex
sys-devel/gettext
sys-devel/gnuconfig
sys-devel/libtool
sys-libs/gdbm
sys-libs/pam
sys-libs/readline
sys-libs/zlib
sys-process/psmisc
sys-process/runit
virtual/libcrypt
virtual/libintl
virtual/openssh
virtual/pkgconfig
virtual/tmpfiles
