ARG from_image
FROM ${from_image} AS base

ENV MISE_QUIET=true

ARG DEPENDENCY_HASH
RUN echo "Dependency hash: $DEPENDENCY_HASH"

ARG git_checkout_branch
ENV GIT_CHECKOUT_BRANCH $git_checkout_branch

ARG git_remote_origin_url
ENV GIT_REMOTE_ORIGIN_URL $git_remote_origin_url

USER ${WORKSPACE_USER}
WORKDIR $WORKSPACE_DIR_NAME

RUN git clone https://gitlab.com/gitlab-org/gitlab-development-kit.git && \
    cd gitlab-development-kit && \
    if [ -n "${GIT_REMOTE_ORIGIN_URL:-}" ]; then \
        git remote set-url origin "${GIT_REMOTE_ORIGIN_URL}.git" && \
        git fetch; \
    fi && \
    if [ -n "${GIT_CHECKOUT_BRANCH:-}" ]; then \
        git checkout "${GIT_CHECKOUT_BRANCH}"; \
    fi && \
    cp /tmp/gdk.yml gdk.yml

FROM base AS install

RUN cd gitlab-development-kit && \
    gdk install shallow_clone=true && \
    (gdk stop || true) && \
    (GDK_KILL_CONFIRM=true gdk kill || true) && \
    ps -ef && \
    mv gitlab/config/secrets.yml . && \
    rm -rf gitlab/ tmp/ && \
    git restore tmp && \
    gdk config set gitlab.rails.bootsnap true && \
    cd "${WORKSPACE_DIR_NAME}" && \
    ln -s "${WORKSPACE_DIR_NAME}/gitlab-development-kit/.tool-versions" "${HOME}/.tool-versions"

FROM install AS final

USER root
RUN cp ./gitlab-development-kit/support/completions/gdk.bash "/etc/profile.d/90-gdk.sh" && \
    chgrp -R 0 "${WORKSPACE_DIR_NAME}" && \
    chmod -R g=u "${WORKSPACE_DIR_NAME}" && \
    chmod g-w "${WORKSPACE_DIR_NAME}/gitlab-development-kit/postgresql/data" && \
    rm -rf /tmp/* && \
    rm -rf ${HOME}/.cache/* && \
    rm -rf ${HOME}/go/pkg/mod/* && \
    rm -rf "${WORKSPACE_DIR_NAME}/gitlab-development-kit/gitaly/_build/cache" && \
    rm -rf "${WORKSPACE_DIR_NAME}/gitlab-development-kit/gitaly/_build/deps" && \
    rm -rf "${WORKSPACE_DIR_NAME}/gitlab-development-kit/gitaly/_build/deps/libgit2/source" && \
    rm -rf "${WORKSPACE_DIR_NAME}/gitlab-development-kit/gitaly/_build/intermediate" && \
    echo "=== Final disk usage ===" && \
    (du -ah / 2>/dev/null | sort -rh | head -20 || true)

USER ${WORKSPACE_USER}

COPY entrypoint.sh /tmp
ENTRYPOINT ["/tmp/entrypoint.sh"]
