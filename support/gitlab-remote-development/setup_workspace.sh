#!/usr/bin/env bash

set -eox pipefail

[[ -z "$GL_WORKSPACE_DOMAIN_TEMPLATE" ]] && { echo "Nothing to do as we're not a GitLab Workspace."; exit 0; }

eval "$(/home/<USER>/.local/bin/mise activate bash --shims)" || { echo "mise activation failed."; exit 1; }

GDK_ROOT_DIR="${GDK_ROOT_DIR:-/projects/gitlab-development-kit}"
GDK_SETUP_FLAG_FILE="${GDK_ROOT_DIR}/.cache/.gdk_setup_complete"
WORKSPACE_DIR_NAME="${WORKSPACE_DIR_NAME:-~/workspace}"
BOOTSTRAPPED_GDK_DIR="${BOOTSTRAPPED_GDK_DIR:-${WORKSPACE_DIR_NAME}/gitlab-development-kit}"
GDK_PORT=$(env | grep SERVICE_PORT_GDK_ | awk -F= '{ print $2 }')
GDK_URL=$(echo "${GL_WORKSPACE_DOMAIN_TEMPLATE}" | sed -r 's/\$\{PORT\}/'"${GDK_PORT}"'/')
MY_IP=$(getent hosts "$(hostname)" | awk '{print $1}')
TIMINGS=()

check_inotify() {
  INOTIFY_WATCHES=$(cat /proc/sys/fs/inotify/max_user_watches)
  INOTIFY_WATCHES_THRESHOLD=524288
  if [[ ${INOTIFY_WATCHES} -lt ${INOTIFY_WATCHES_THRESHOLD} ]]; then
    echo "fs.inotify.max_user_watches is less than ${INOTIFY_WATCHES_THRESHOLD}. Please set this on your node."
    echo "See https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/307 and"
    echo "https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/advanced.md#install-dependencies-for-other-linux-distributions"
    echo "for details."

    exit 1
  fi
}

install_gems() {
  echo "Installing Gems in ${GDK_ROOT_DIR}"
  bundle install
  pushd gitlab
  echo "Installing Gems in ${GDK_ROOT_DIR}/gitlab"
  bundle install
  popd
}

measure_time() {
  local start=$SECONDS
  "$@"
  local duration=$((SECONDS - start))
  TIMINGS+=("$1: $duration seconds")
}

print_timings() {
  DURATION=$SECONDS
  echo "Total Duration: $((DURATION / 60)) minutes and $((DURATION % 60)) seconds."
  echo "Execution times for each function:"
  printf '%s\n' "${TIMINGS[@]}"
}

clone_gitlab() {
  if [ -d "/projects/gitlab" ]; then
    echo "Found existing gitlab at /projects/gitlab, creating symlink."
    ln -snf /projects/gitlab gitlab
    gdk config set gitlab.auto_update false
    gdk config set gitlab.default_branch "$(git -C /projects/gitlab rev-parse --abbrev-ref HEAD)"
  else
    echo "Cloning gitlab-org/gitlab"
    make gitlab/.git
  fi
  cp "${BOOTSTRAPPED_GDK_DIR}/secrets.yml" gitlab/config
}

copy_items_from_bootstrap() {
  interesting_items=(
    "gdk.yml"
    ".cache"
    "clickhouse"
    "consul"
    "gdk-config.mk"
    "gitaly"
    ".gitlab-bundle"
    ".gitlab-lefthook"
    "gitlab-pages"
    "gitlab-runner-config.toml"
    "gitlab-shell"
    ".gitlab-shell-bundle"
    ".gitlab-translations"
    ".gitlab-yarn"
    "localhost.crt"
    "localhost.key"
    "log"
    "pgbouncers"
    "postgresql"
    "Procfile"
    "redis"
    "registry"
    "registry_host.crt"
    "registry_host.key"
    "repositories"
    "services"
    "sv"
  )

  if [[ "${GDK_ROOT_DIR}" == "${BOOTSTRAPPED_GDK_DIR}" ]]; then
    echo "Skipping moving bootstrapped GDK items to persistent storage."
    return 0
  fi

  for item in "${interesting_items[@]}"; do
    echo "Moving bootstrapped GDK item: ${item}"
    rm -rf "${GDK_ROOT_DIR:?}/${item}" || true
    [ -e "${WORKSPACE_DIR_NAME}/gitlab-development-kit/${item}" ] && mv "${WORKSPACE_DIR_NAME}/gitlab-development-kit/${item}" .
  done
}

reconfigure() {
  gdk rake generate_config_files
  gdk reconfigure
}

migrate_db(){
  gdk rake gitlab-db-migrate
  gdk stop
}

update_gdk() {
  gdk update
}

restart_gdk() {
  gdk stop
  gdk start
}

configure_license(){
  echo "Configure license"

  if [ -z "$GITLAB_ACTIVATION_CODE" ]; then
    echo "No envvar GITLAB_ACTIVATION_CODE provided. Skipping."
    return 0
  else
    pushd gitlab
    echo "Activating Cloud license via envvar GITLAB_ACTIVATION_CODE..."
    bundle exec rake "gitlab:license:load[verbose]" || true
    popd
  fi
}

configure_gdk_env() {
  echo "$GDK_CONFIG" | yq eval '.' -P | yq eval-all '. as $item ireduce ({}; . * $item)' -i gdk.yml -
  gdk config set gitlab.rails.hostname "${GDK_URL}"
  gdk config set gitlab.rails.allowed_hosts "$(hostname)"
  gdk config set listen_address "${MY_IP}"
  if [[ "$(git config user.email)" == *"gitlab.com" ]]; then
      gdk config set telemetry.enabled true
      local hmac_secret="${TELEMETRY_HMAC_SECRET:-}"
      if [[ -z "${hmac_secret}" ]]; then
        echo "TELEMETRY_HMAC_SECRET is not set. Telemetry username will be hashed without key."
      fi
      username_hash=$(echo -n "$(git config user.email)" | openssl mac -macopt hexkey:"${hmac_secret}" -digest sha256 HMAC | cut -c1-32 | tr '[:upper:]' '[:lower:]')
      gdk config set telemetry.username "$username_hash"
  else
      echo "Not a GitLab email, skipping telemetry"
  fi
}

write_flag_file() {
    local exit_code=$?
    mkdir -p "$(dirname "${GDK_SETUP_FLAG_FILE}")"
    echo -e "$exit_code $SECONDS" > "${GDK_SETUP_FLAG_FILE}"
    exit $exit_code
}

setup() {
  measure_time check_inotify
  measure_time clone_gitlab
  measure_time copy_items_from_bootstrap
  measure_time configure_gdk_env
  measure_time install_gems
  measure_time reconfigure
  measure_time configure_license
  measure_time migrate_db
  measure_time update_gdk
  measure_time restart_gdk
  print_timings
}

if [[ ! -f "${GDK_SETUP_FLAG_FILE}" ]]; then
  trap write_flag_file EXIT
  pushd "${GDK_ROOT_DIR}"
  setup
  popd
fi
