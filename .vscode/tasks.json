{"version": "2.0.0", "tasks": [{"type": "shell", "command": "${workspaceFolder}/scripts/initialize-workspaces.sh", "isBackground": false, "group": "none", "problemMatcher": [], "label": "Workspaces: Set up workspaces", "runOptions": {"runOn": "folderOpen"}, "presentation": {"close": true}}, {"type": "npm", "script": "watch:desktop", "isBackground": true, "group": "build", "problemMatcher": ["$esbuild-watch", "$tsc-watch"], "label": "Build and watch desktop sources"}, {"type": "npm", "script": "prepare:test:integration", "group": {"kind": "build"}, "problemMatcher": ["$tsc"], "label": "npm: prepare:test:integration"}, {"type": "npm", "script": "lint", "problemMatcher": ["$eslint-stylish"], "label": "npm: lint", "detail": "eslint --ext .js --ext .ts . && prettier --check '**/*.{js,ts,vue,json}' && npm run --prefix webviews/vue3 lint && npm run --prefix webviews/vue2 lint"}, {"label": "Terminate All Tasks", "command": "echo ${input:terminate}", "type": "shell", "problemMatcher": []}], "inputs": [{"id": "terminate", "type": "command", "command": "workbench.action.tasks.terminate", "args": "terminateAll"}]}