#!/usr/bin/env ruby

# frozen_string_literal: true

require 'yaml'
require 'pathname'

$LOAD_PATH.unshift(File.expand_path('../lib', __dir__))
require 'gitlab_development_kit'

# Gitlab Development Kit CLI launcher
#
# Note to contributors: this script must not change (much) because it is
# installed outside the gitlab-development-kit repository with 'gem
# install'. Edit lib/gdk.rb to define new commands.
module GDK
  DEFAULT_INIT_DIRECTORY = File.join(Dir.pwd, 'gdk')

  module CommandBasic
    class Main
      def initialize(args)
        @args = args
      end

      def run
        return GDK::CommandBasic::Version.new.run if gdk_version?

        if gdk_root
          require(gdk_root.join('lib/gdk.rb'))
          GDK.main
        else
          warn_not_gdk_dir
          false
        end
      end

      private

      attr_reader :args

      def warn_not_gdk_dir
        warn <<~NOT_A_GDK_DIR

          The current working directory is not inside a gitlab-development-kit
          installation. Use 'cd' to go to your gitlab-development-kit.

          # Default: #{DEFAULT_INIT_DIRECTORY}

        NOT_A_GDK_DIR
      end

      def gdk_root
        @gdk_root ||= find_root(Pathname.new(ENV.fetch('PWD', nil)))
      end

      def gdk_version?
        # If gdk_root == true, fall through to allow lib/gdk.rb to handle
        %w[version --version].include?(args.first) && !gdk_root
      end

      def find_root(current)
        if current.join('GDK_ROOT').exist?
          current.realpath
        elsif fs_root?(current)
          nil
        else
          find_root(current.join('..'))
        end
      end

      def fs_root?(path)
        path.realpath.root? || path.realpath.to_s == path.join('..').realpath.to_s
      end
    end

    class Version
      def run
        puts GDK::VERSION
        true
      end
    end
  end
end

exit(GDK::CommandBasic::Main.new(ARGV).run)
