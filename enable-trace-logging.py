#!/usr/bin/env python3
"""
Script to enable detailed trace logging for debugging DAP flow.
This will help you see trace data in the service logs.
"""

import os
import json
from pathlib import Path

def update_ai_gateway_logging():
    """Update AI Gateway logging to show trace details."""
    env_file = Path("gitlab-ai-gateway/.env")
    
    # Add detailed logging for traces
    additional_config = """
# Enhanced logging for trace debugging
AIGW_LOGGING__ENABLE_TRACE_LOGGING=true
AIGW_LOGGING__TRACE_LEVEL=debug
LANGSMITH_HIDE_INPUTS=false
LANGSMITH_HIDE_OUTPUTS=false
LANGSMITH_TRACING_SAMPLING_RATE=1.0
LANGCHAIN_CALLBACKS_BACKGROUND=false

# Python logging for LangSmith
PYTHONPATH=/Users/<USER>/Developer/gitlab/gdk/gitlab-ai-gateway
********************
"""
    
    with open(env_file, 'a') as f:
        f.write(additional_config)
    
    print(f"✅ Updated {env_file} with enhanced trace logging")

def create_trace_monitor():
    """Create a real-time trace monitor script."""
    monitor_script = """#!/usr/bin/env python3
import time
import json
import sys
from pathlib import Path

def monitor_traces():
    log_files = [
        "log/duo-workflow-service/current",
        "log/gitlab-ai-gateway/gateway_debug.log"
    ]
    
    print("🔍 Monitoring traces in real-time...")
    print("Press Ctrl+C to stop")
    
    # Keep track of last position in each file
    positions = {}
    
    try:
        while True:
            for log_file in log_files:
                log_path = Path(log_file)
                if not log_path.exists():
                    continue
                    
                # Get current position
                current_pos = positions.get(log_file, 0)
                
                with open(log_path, 'r') as f:
                    f.seek(current_pos)
                    new_lines = f.readlines()
                    positions[log_file] = f.tell()
                
                # Filter for trace-related lines
                for line in new_lines:
                    if any(keyword in line.lower() for keyword in [
                        'trace', 'langsmith', 'run_tree', 'workflow', 
                        'duo', 'agent', 'tool', 'llm'
                    ]):
                        timestamp = time.strftime("%H:%M:%S")
                        print(f"[{timestamp}] {Path(log_file).name}: {line.strip()}")
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\\n👋 Stopped monitoring")

if __name__ == "__main__":
    monitor_traces()
"""
    
    with open("monitor-traces.py", "w") as f:
        f.write(monitor_script)
    
    os.chmod("monitor-traces.py", 0o755)
    print("✅ Created monitor-traces.py for real-time trace monitoring")

def create_test_dap_request():
    """Create a script to test DAP requests and generate traces."""
    test_script = """#!/usr/bin/env python3
import requests
import json
import time

def test_dap_request():
    # Test the AI Gateway directly
    ai_gateway_url = "http://127.0.0.1:5052"
    
    # Test health endpoint first
    try:
        response = requests.get(f"{ai_gateway_url}/monitoring/health")
        print(f"AI Gateway health: {response.status_code}")
    except Exception as e:
        print(f"AI Gateway not accessible: {e}")
        return
    
    # Test a simple chat completion that should generate traces
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"  # Adjust as needed
    }
    
    payload = {
        "model": "claude-3-haiku-20240307",
        "messages": [
            {"role": "user", "content": "Hello, this is a test message for tracing"}
        ],
        "max_tokens": 100
    }
    
    try:
        print("🚀 Sending test request to AI Gateway...")
        response = requests.post(
            f"{ai_gateway_url}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Request successful - check logs for traces")
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error making request: {e}")

if __name__ == "__main__":
    test_dap_request()
"""
    
    with open("test-dap-request.py", "w") as f:
        f.write(test_script)
    
    os.chmod("test-dap-request.py", 0o755)
    print("✅ Created test-dap-request.py to generate test traces")

def main():
    print("🔧 Setting up local trace debugging...")
    
    # Update AI Gateway logging
    update_ai_gateway_logging()
    
    # Create monitoring scripts
    create_trace_monitor()
    create_test_dap_request()
    
    print("\n📋 Next steps:")
    print("1. Restart GDK services: gdk restart")
    print("2. Start trace monitoring: ./monitor-traces.py")
    print("3. In another terminal, test DAP: ./test-dap-request.py")
    print("4. Or use VS Code extension to trigger DAP requests")
    print("5. View stored traces: python view-local-traces.py")

if __name__ == "__main__":
    main()
