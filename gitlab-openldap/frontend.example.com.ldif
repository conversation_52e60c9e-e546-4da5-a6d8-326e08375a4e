# source: https://help.ubuntu.com/10.04/serverguide/openldap-server.html

# Create top-level object in domain
dn: dc=example,dc=com
objectClass: top
objectClass: dcObject
objectclass: organization
o: Example Organization
dc: Example
description: LDAP Example

# Admin user.
dn: cn=admin,dc=example,dc=com
objectClass: simpleSecurityObject
objectClass: organizationalRole
cn: admin
description: LDAP administrator
# hashed value for 'password'
userPassword: {SSHA}ICMhr6Jxt5bk2awD7HL7GxRTM3BZ1pFI

dn: ou=people,dc=example,dc=com
objectClass: organizationalUnit
ou: people

dn: ou=groups,dc=example,dc=com
objectClass: organizationalUnit
ou: groups

dn: uid=john,ou=people,dc=example,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
uid: john
sn: <PERSON>e
givenName: <PERSON>
cn: <PERSON>
displayName: <PERSON>
uidNumber: 1000
gidNumber: 10000
# hashed value for 'password'
userPassword: {SSHA}qqLFjamdd1cru4RV815+FiSxh/54rfbd
gecos: John Doe
loginShell: /bin/bash
homeDirectory: /home/<USER>
shadowExpire: -1
shadowFlag: 0
shadowWarning: 7
shadowMin: 8
shadowMax: 999999
shadowLastChange: 10877
mail: <EMAIL>
postalCode: 31000
l: Toulouse
o: Example
mobile: +33 (0)6 xx xx xx xx
homePhone: +33 (0)5 xx xx xx xx
title: System Administrator
postalAddress:
initials: JD

dn: uid=mary,ou=people,dc=example,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
uid: Mary
sn: Jane
givenName: Mary Jane
cn: Mary Jane
displayName: Mary Jane
uidNumber: 1001
gidNumber: 10001
# hashed value for 'password'
userPassword: {SSHA}qqLFjamdd1cru4RV815+FiSxh/54rfbd
mail: <EMAIL>
gecos: Mary
loginShell: /bin/bash
homeDirectory: /home/<USER>
shadowExpire: -1
shadowFlag: 0
shadowWarning: 7
shadowMin: 8
shadowMax: 999999
shadowLastChange: 10877
postalCode: 31000
l: Toulouse
o: Example
mobile: +33 (0)6 xx xx xx xx
homePhone: +33 (0)5 xx xx xx xx
title: System Administrator
postalAddress:
initials: JD

dn: cn=example,ou=groups,dc=example,dc=com
objectClass: posixGroup
cn: example
gidNumber: 10000

dn: cn=group1,ou=groups,dc=example,dc=com
objectClass: groupofnames
ou: groups
cn: group1
member: uid=john,ou=people,dc=example,dc=com
member: uid=mary,ou=people,dc=example,dc=com

dn: cn=group2,ou=groups,dc=example,dc=com
objectClass: groupofnames
ou: groups
cn: group2
member: uid=john,ou=people,dc=example,dc=com
