# source: https://help.ubuntu.com/10.04/serverguide/openldap-server.html

# Create top-level object in domain
dn: dc=example-alt,dc=com
objectClass: top
objectClass: dcObject
objectclass: organization
o: Example Organization
dc: Example-alt
description: LDAP Example

# Admin user.
dn: cn=admin,dc=example-alt,dc=com
objectClass: simpleSecurityObject
objectClass: organizationalRole
cn: admin
description: LDAP administrator
# hashed value for 'password'
userPassword: {SSHA}ICMhr6Jxt5bk2awD7HL7GxRTM3BZ1pFI

dn: ou=people,dc=example-alt,dc=com
objectClass: organizationalUnit
ou: people

dn: ou=groups,dc=example-alt,dc=com
objectClass: organizationalUnit
ou: groups

dn: uid=bob,ou=people,dc=example-alt,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
uid: bob
sn: Doe
givenName: bob
cn: bob <PERSON><PERSON>
displayName: bob Doe
uidNumber: 1000
gidNumber: 10000
# hashed value for 'password'
userPassword: {SSHA}qqLFjamdd1cru4RV815+FiSxh/54rfbd
gecos: bob Doe
loginShell: /bin/bash
homeDirectory: /home/<USER>
shadowExpire: -1
shadowFlag: 0
shadowWarning: 7
shadowMin: 8
shadowMax: 999999
shadowLastChange: 10877
mail: <EMAIL>
postalCode: 31000
l: Toulouse
o: Example
mobile: +33 (0)6 xx xx xx xx
homePhone: +33 (0)5 xx xx xx xx
title: System Administrator
postalAddress:
initials: JD

dn: uid=alice,ou=people,dc=example-alt,dc=com
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
uid: alice
sn: Jane
givenName: alice Jane
cn: alice Jane
displayName: alice Jane
uidNumber: 1001
gidNumber: 10001
# hashed value for 'password'
userPassword: {SSHA}qqLFjamdd1cru4RV815+FiSxh/54rfbd
mail: <EMAIL>
gecos: alice
loginShell: /bin/bash
homeDirectory: /home/<USER>
shadowExpire: -1
shadowFlag: 0
shadowWarning: 7
shadowMin: 8
shadowMax: 999999
shadowLastChange: 10877
postalCode: 31000
l: Toulouse
o: Example
mobile: +33 (0)6 xx xx xx xx
homePhone: +33 (0)5 xx xx xx xx
title: System Administrator
postalAddress:
initials: JD

dn: cn=example,ou=groups,dc=example-alt,dc=com
objectClass: posixGroup
cn: example
gidNumber: 10000

dn: cn=group-a,ou=groups,dc=example-alt,dc=com
objectClass: groupofnames
ou: groups
cn: group-a
member: uid=bob,ou=people,dc=example-alt,dc=com
member: uid=alice,ou=people,dc=example-alt,dc=com

dn: cn=group-b,ou=groups,dc=example-alt,dc=com
objectClass: groupofnames
ou: groups
cn: group-b
member: uid=bob,ou=people,dc=example-alt,dc=com
