# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
trim_trailing_whitespace = true
insert_final_newline = true
indent_size = 2
indent_style = space

# Matches multiple files with brace expansion notation
# Set default charset
[**/*.rb]
charset = utf-8

# Protect the ascii-art!
[{lib/gdk/logo.rb,gdk.example.yml}]
end_of_line = unset
trim_trailing_whitespace = unset
insert_final_newline = unset
indent_size = unset
indent_style = unset

# Tab indentation (no size specified)
[Makefile]
indent_style = tab
indent_size = unset

[*.mk]
indent_style = tab
indent_size = unset

[GDK_ROOT]
end_of_line = unset
trim_trailing_whitespace = unset
insert_final_newline = unset
indent_size = unset
indent_style = unset

[**/.gitkeep]
end_of_line = unset
trim_trailing_whitespace = unset
insert_final_newline = unset
indent_size = unset
indent_style = unset

[**/*.md]
indent_size = unset

[Gemfile.lock]
indent_style = unset
indent_size = unset
tab_width = unset
end_of_line = unset
max_line_length = unset
trim_trailing_whitespace = unset
insert_final_newline = unset

# ----------------------------------------------------------------------
# This rule should always stay at the bottom for greatest precedence
# ----------------------------------------------------------------------

# Don't apply these rules to any nested projects, except gitlab, which defines its own root level config
[{go-gitlab-shell,gitaly,docs-gitlab-com,gitlab-workhorse,gitlab-pages}/**]
indent_style = unset
indent_size = unset
tab_width = unset
end_of_line = unset
max_line_length = unset
trim_trailing_whitespace = unset
insert_final_newline = unset
