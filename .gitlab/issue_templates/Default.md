<!---
Please read this!

If you want to request a new feature, choose the "Feature Proposal" template.

Before opening a new issue, make sure to search for keywords in the issues
filtered by the "bug" label:

- https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/issues?label_name%5B%5D=type%3A%3Abug

and verify the issue you're about to submit isn't a duplicate.
--->

### Checklist

<!-- Please test the latest versions, that will remove the possibility that you see a bug that is fixed in a newer version. -->

- [ ] I'm using the latest version of the extension ([see the latest version in the right column of this page](https://marketplace.visualstudio.com/items?itemName=GitLab.gitlab-workflow))
  - Extension version: _Put your extension version here_
- [ ] I'm using the latest VS Code version ([find the latest version here](https://github.com/microsoft/vscode/releases))
  - VS Code version: _Put your VS Code version here_
- [ ] I'm using a supported version of GitLab ([see README for the supported version](https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/blob/main/README.md#minimum-supported-version))
  - GitLab version: _Put your GitLab version here, or say "happens on `gitlab.com`"_

### Summary

<!-- Summarize the bug encountered concisely -->

### Steps to reproduce

<!-- How one can reproduce the issue - this is very important -->

### What is the current _bug_ behavior?

<!-- What actually happens -->

### What is the expected _correct_ behavior?

<!-- What you should see instead -->

### Relevant logs and/or screenshots

<!-- Logs can be found by running `GitLab: Show extension logs` command (using `cmd+shift+p`). Please enable `"gitlab.debug": true` in your `settings.json`. Debug mode will improve error stack traces and adds debug log messages. -->

### Possible fixes

<!-- If you can, link to the line of code that might be responsible for the problem -->

/label ~"type::bug" ~"devops::ai-powered" ~"group::editor extensions" ~"Category:Editor Extensions" ~"Editor Extensions::VS Code" ~"section::dev"
