## Overview

<!-- Details of the issue. Include any console output or screenshots. -->

## Impacted categories

The following categories relate to this issue:

- [ ] ~"gdk-reliability" - e.g. When a GDK action fails to complete.
- [ ] ~"gdk-usability" - e.g. Improvements or suggestions around how the GDK functions.
- [ ] ~"gdk-performance" - e.g. When a GDK action is slow or times out.

## Steps to replicate (optional)

<!-- Clear steps of how to replicate the issue. -->

## Proposal (optional)

<!-- Description of any proposal you might have. -->

## Environment (optional)

- Operating system name: ```<!-- output of `uname -a` command -->```
- Architecture: ```<!-- output of `arch` or `uname -m` command -->```
- The contents of your `gdk.yml` (if any)
- Ruby version: ```<!-- output of `ruby --version` command -->```
- GDK version: ```<!-- output of `git rev-parse --short HEAD` command -->```

/label ~"Category:GDK" ~"group::development tooling"

<!-- Thanks for contributing to GDK ♥️ -->

<!-- template sourced from https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/.gitlab/issue_templates/Default.md -->
