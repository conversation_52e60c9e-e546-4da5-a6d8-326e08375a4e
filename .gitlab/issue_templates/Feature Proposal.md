<!---
Please read this!

Before opening a new issue, make sure to search for keywords in the issues
filtered by the "feature" label:

- https://gitlab.com/gitlab-org/gitlab-vscode-extension/-/issues?label_name%5B%5D=type%3A%3Afeature

and verify the issue you're about to submit isn't a duplicate.
--->

### Problem to solve

<!-- What problem do we solve? Try to define the who/what/why of the opportunity as a user story. For example, "As a (who), I want (what), so I can (why/value)." -->

### Proposal

<!-- How are we going to solve the problem? -->

### Further details

<!-- Include examples, use cases, benefits, goals, or any other details that will help us understand the problem better. -->

### Links / references

/label ~"type::feature" ~"devops::ai-powered" ~"group::editor extensions" ~"Category:Editor Extensions" ~"Editor Extensions::VS Code" ~"section::dev"
