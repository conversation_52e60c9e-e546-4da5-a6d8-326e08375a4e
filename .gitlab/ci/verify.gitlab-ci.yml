# Jobs that are generally only executable by CI and not locally.
#
.verify-base-job:
  extends:
    - .verify-job-cached_variables
    - .rules:code-changes-verify
  image: ${VERIFY_IMAGE}
  stage: verify
  needs:
    - build-verify-image
  variables:
    GIT_STRATEGY: none
  before_script:
    - cd /home/<USER>/gdk
    - !reference [.default-before_script, before_script]
  after_script:
    - !reference [.default-after_script, after_script]
  tags:
    - gitlab-org-docker
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/
    expire_in: 2 days
    when: always
  timeout: 2h

start:
  extends: .verify-base-job
  script:
    - run_timed_command "support/ci/verify-start"

doctor:
  extends: .verify-base-job
  script:
    - run_timed_command "support/ci/verify-doctor"

reconfigure:
  extends: .verify-base-job
  script:
    - run_timed_command "support/ci/verify-reconfigure"

reset-data:
  extends: .verify-base-job
  script:
    - run_timed_command "support/ci/verify-reset-data"

pristine:
  extends: .verify-base-job
  script:
    - run_timed_command "support/ci/verify-pristine"

update:
  extends: .verify-base-job
  tags:
    - saas-linux-large-amd64
  script:
    - run_timed_command "support/ci/verify-update"
  after_script:
    - !reference [.verify-base-job, after_script]
    - mkdir -p public/gdk
    - mv /home/<USER>/gdk/$GITLAB_LAST_VERIFIED_SHA_PATH public/gdk
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/
      - public/gdk/*
    expire_in: 2 days
    when: always

gitpod-docker-image:
  extends:
    - .docker:build-docker-in-docker
    - .rules:gitpod-code-changes
  stage: verify
  needs:
    - build-gitpod-workspace-image
  script:
    - run_timed_command "support/ci/verify-gitpod-docker-image"
  artifacts:
    paths:
      - ./artifacts/log/*
    expire_in: 2 days
  timeout: 1h

grd-docker-image:
  extends:
    - .docker:build-docker-in-docker
    - .rules:gitlab-remote-code-changes
  stage: verify
  tags:
    - saas-linux-large-amd64
  needs:
    - build-gitlab-remote-workspace-image
  script:
    - run_timed_command "support/ci/verify-grd-docker-image $WORKSPACE_REPO $WORKSPACE_REF $GDK_ROOT_DIR"
  parallel:
    matrix:
      - WORKSPACE_REPO: gitlab-org/gitlab
        WORKSPACE_REF: master
        GDK_ROOT_DIR: /home/<USER>/workspace/gitlab-development-kit
      - WORKSPACE_REPO: gitlab-org/gitlab-development-kit
        WORKSPACE_REF: main
        GDK_ROOT_DIR: /projects/gitlab-development-kit
  artifacts:
    when: always
    paths:
      - ./artifacts/**/*
    expire_in: 2 days
  timeout: 2h

geo-install:
  extends:
    - .verify-base-job
    - .rules:geo-install
  tags:
    - saas-linux-large-amd64
  script:
    - run_timed_command "support/ci/verify-geo"
  after_script:
    - mkdir -p $CI_PROJECT_DIR/gitlab2_log $CI_PROJECT_DIR/gdk2_log
    - cp -rf /home/<USER>/gdk2/gitlab/log/*.log $CI_PROJECT_DIR/gitlab2_log/
    - cp -rf /home/<USER>/gdk2/log/ $CI_PROJECT_DIR/gdk2_log/
  artifacts:
    paths:
      - ./gitlab2_log
      - ./gdk2_log
    expire_in: 2 days
    when: always

cells:
  extends: .verify-base-job
  tags:
    - saas-linux-large-amd64
  script:
    - run_timed_command "support/ci/verify-cells"
  after_script:
    - !reference [.verify-base-job, after_script]
    - mkdir -p gdk_cells_log gitlab_cells_log
    - mv /home/<USER>/gdk/gitlab-cells/cell-2/gitlab/log/*.log gitlab_cells_log/
    - mv /home/<USER>/gdk/gitlab-cells/cell-2/log/ gdk_cells_log/
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/
      - gitlab_cells_log/
      - gdk_cells_log/
      - $GITLAB_LAST_VERIFIED_SHA_PATH
    expire_in: 2 days
    when: always

sandbox:
  extends: .verify-base-job
  tags:
    - saas-linux-large-amd64
  script:
    - run_timed_command "support/ci/verify-sandbox"
  after_script:
    - !reference [.verify-base-job, after_script]
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/
    expire_in: 2 days
    when: always
