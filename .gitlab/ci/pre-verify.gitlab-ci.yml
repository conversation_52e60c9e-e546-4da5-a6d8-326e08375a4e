# Jobs that are generally only executable by CI and not locally.
#
build-verify-image:
  extends:
    - .cached-job
    - .build-job
    - .rules:code-changes-verify
  stage: pre-verify
  needs:
    - release-image
  script:
    - ./support/docker ci-build-verify-image

build-integration-image:
  stage: pre-verify
  extends:
    - .cached-job
    - .build-job
    - .rules:build-integration-image
  needs: []
  script:
    - ./support/docker ci-build-integration-image
