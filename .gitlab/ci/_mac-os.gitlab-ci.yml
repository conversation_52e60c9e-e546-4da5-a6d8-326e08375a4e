.installation:mac-os-job:
  tags:
    - saas-macos-large-m2pro
  extends:
    - .rules:novel
  image: macos-15-xcode-16
  variables:
    MISE_HTTP_TIMEOUT: "60s"
    MISE_FETCH_REMOTE_VERSIONS_TIMEOUT: "60s"
    GDK_DIR: /Users/<USER>/builds/gitlab-org/gitlab-development-kit
  needs: []
  before_script:
    # install prerequisites
    - brew install make moreutils
  after_script:
    - tail -100 gdk-install.log
  artifacts:
    paths:
      - gdk-install.log
    expire_in: 2 days
    when: always

.verification:mac-os-job:
  after_script:
    - mv ${GDK_DIR}/gitlab/log/*.log gitlab_log/
    - mv ${GDK_DIR}/log/ gdk_log/
