.novel-build-job:
  stage: novel
  extends:
    - .docker:build-docker-in-docker
    - .rules:novel
  needs: []
  script:
    - ./support/docker ci-build-novel-image "${BUILD_OS_VERSION}"

novel:linux-amd64:
  extends: .novel-build-job
  parallel:
    matrix:
      - BUILD_OS_VERSION:
          - arch
          - ubuntu

novel:macos:
  extends: .installation:mac-os-job
  stage: novel
  variables:
    GIT_STRATEGY: none
  script:
    # install the GDK
    - echo "installing GDK from https://gitlab.com/gitlab-org/gitlab-development-kit/-/raw/${CI_COMMIT_REF_NAME}/support/install"
    - curl --fail "https://gitlab.com/gitlab-org/gitlab-development-kit/-/raw/${CI_COMMIT_REF_NAME}/support/install" | bash -s - gdk "${CI_COMMIT_REF_NAME}" "true" 2>&1 | ts -s -m > gdk-install.log

update:macos:
  extends:
    - .installation:mac-os-job
    - .verification:mac-os-job
  stage: novel
  script:
    - ./support/install . "${CI_COMMIT_REF_NAME}" "true" 2>&1 | ts -s -m > gdk-install.log
    - mkdir -p gitlab_log gdk_log
    - eval "$(mise activate bash)"
    # source utils
    - . support/ci/utils.sh;
    - run_timed_command "support/ci/verify-update"
  artifacts:
    paths:
      - gitlab_log/
      - gdk_log/

postgres-upgrade:macos:
  extends:
    - .installation:mac-os-job
    - .verification:mac-os-job
    - .rules:postgres-upgrade
  stage: novel
  script:
    - |
      CURRENT_POSTGRES_VERSION=$(grep '^postgres ' .tool-versions | awk '{print $2}')
      echo "Current version: $CURRENT_POSTGRES_VERSION"
      echo "Target version: $TARGET_POSTGRES_VERSION"
      if [ "$CURRENT_POSTGRES_VERSION" = "$TARGET_POSTGRES_VERSION" ]; then
        echo "Skipping job since the current is the same as the target version ($TARGET_POSTGRES_VERSION)."
        exit 0
      fi
    - ./support/install . "${CI_COMMIT_REF_NAME}" "true" 2>&1 | ts -s -m > gdk-install.log
    - mkdir -p gitlab_log gdk_log
    - eval "$(mise activate bash)"
    - |
      CURRENT_POSTGRES_VERSION=$(grep '^postgres ' .tool-versions | awk '{print $2}')
      CURRENT_RUNNING_POSTGRES_VERSION=$(gdk psql -t -c "SHOW server_version;" | tr -d ' ')
      echo "Current running version: $CURRENT_RUNNING_POSTGRES_VERSION"
      if [[ "$CURRENT_POSTGRES_VERSION" != "$CURRENT_RUNNING_POSTGRES_VERSION" ]]; then
        echo "Skipping job because psql is running version $CURRENT_RUNNING_POSTGRES_VERSION instead of defined version $CURRENT_POSTGRES_VERSION";
        exit 0
      fi
    - source support/ci/functions.sh
    - init
    - cd_into_checkout_path "gitlab"
    - sed -i '' -E "s/(postgres) ([0-9]+\.[0-9]+) ([0-9]+\.[0-9]+)/\1 $TARGET_POSTGRES_VERSION \2/" .tool-versions
    - cd_into_checkout_path
    - sed -i '' -E "s/(postgres) ([0-9]+\.[0-9]+) ([0-9]+\.[0-9]+)/\1 $TARGET_POSTGRES_VERSION \2/" .tool-versions
    - . support/ci/utils.sh;
    - run_timed_command "gdk config set pgvector.enabled true"
    - run_timed_command "GDK_SELF_UPDATE=0 gdk update"
    - run_timed_command "gdk start"
    - run_timed_command "gdk restart postgresql"
    # use vite instead of webpack
    - run_timed_command "gdk stop webpack rails-web"
    - run_timed_command "gdk config set webpack.enabled false"
    - run_timed_command "gdk config set vite.enabled true"
    - run_timed_command "gdk reconfigure"
    - run_timed_command "gdk restart vite rails-web"
    - run_timed_command "test_url"
    - |
      CURRENT_RUNNING_POSTGRES_VERSION=$(gdk psql -t -c "SHOW server_version;" | tr -d ' ')
      echo "Now running on version $CURRENT_RUNNING_POSTGRES_VERSION"
      if [[ "$CURRENT_RUNNING_POSTGRES_VERSION" != "$TARGET_POSTGRES_VERSION" ]]; then
        echo "Upgrade failed: psql is running version $CURRENT_RUNNING_POSTGRES_VERSION instead of desired version $TARGET_POSTGRES_VERSION";
        exit 0
      fi
  artifacts:
    paths:
      - gdk-install.log
      - gitlab_log/
      - gdk_log/
