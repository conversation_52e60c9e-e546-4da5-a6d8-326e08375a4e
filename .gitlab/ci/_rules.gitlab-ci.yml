#############################
# Conditions (Alphabetical) #
#############################
.if-default-branch-refs: &if-default-branch-refs
  if: '$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH && $CI_MERGE_REQUEST_IID == null'

.if-fork: &if-fork
  if: '$CI_PROJECT_NAMESPACE !~ /^gitlab(-org)?($|\/)/'

.if-maintenance: &if-maintenance
  if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "maintenance"'

.if-merge-request: &if-merge-request
  if: '$CI_MERGE_REQUEST_IID'

.if-merge-request-expedited: &if-merge-request-expedited
  if: '$CI_MERGE_REQUEST_LABELS =~ /pipeline::expedited/'

.if-merge-request-labels-run-compile-jobs: &if-merge-request-labels-run-compile-jobs
  if: '$CI_MERGE_REQUEST_LABELS =~ /pipeline:run-compile-jobs/'

.if-nightly: &if-nightly
  if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "nightly"'

.skip-maintenance-and-nightly:
  rules:
    - <<: *if-maintenance
      when: never
    - <<: *if-nightly
      when: never

###################################
# Changes patterns (Alphabetical) #
###################################
.code-changes: &code-changes
  - "**/*.{rb,erb,sh,yml,example,types}"
  - "{lib,gem,bin}/**/*"
  - "{spec,support}/**/*"
  - ".mise-version"
  - ".ruby-version"
  - ".tool-versions"
  - "mise.toml"
  - ".gdkrc"
  - "bin/gdk-shell"
  - ".gitlab-ci.yml"
  - ".gitlab/ci/**/*"
  - "Gemfile{,.lock}"
  - "Brewfile"
  - "Makefile"
  - "Rakefile"
  - "Dockerfile"
  - "**/*/Dockerfile"
  - "packages*.txt"
  - "**/*/Makefile"
  - "Vagrantfile"
  - "gitlab-openldap/**/*"
  - "vagrant/assets/**/*"
  - "GITLAB_HTTP_ROUTER_VERSION"
  - "GITLAB_TOPOLOGY_SERVICE_VERSION"

.docs-changes: &docs-changes
  - "**/*.{md}"
  - "{doc}/**/*"
  - ".markdownlint.yml"
  - "package.json"
  - "yarn.lock"

.docs-code-changes: &docs-code-changes
  - "**/*.{md}"
  - "{doc}/**/*"
  - "doc-site/**/*"
  - "**/*.{rb,erb,sh,yml,example,types}"
  - "{lib,gem,bin}/**/*"
  - "{spec,support}/**/*"
  - ".mise-version"
  - ".ruby-version"
  - ".tool-versions"
  - "mise.toml"
  - ".gdkrc"
  - "bin/gdk-shell"
  - ".gitlab-ci.yml"
  - ".gitlab/ci/**/*"
  - "Gemfile{,.lock}"
  - "Brewfile"
  - "Rakefile"
  - "Dockerfile"
  - "**/*/Dockerfile"
  - "packages*.txt"
  - "**/*/Makefile"
  - "Vagrantfile"
  - "gitlab-openldap/**/*"
  - "vagrant/assets/**/*"

.gitlab-remote-code-changes: &gitlab-remote-code-changes
  - ".mise-version"
  - ".tool-versions"
  - "mise.toml"
  - "packages_debian.txt"
  - "support/bootstrap"
  - "support/bootstrap-common.sh"
  - "support/docker-build"
  - "support/gitlab-remote-development/**/*"

.gitlab-gdk-in-a-box-code-changes: &gitlab-gdk-in-a-box-code-changes
  - "support/gdk-in-a-box/container/**/*"

.gitlab-remote-workspace-base-changes: &gitlab-remote-workspace-base-changes
  - ".mise-version"
  - ".tool-versions"
  - "mise.toml"
  - "packages_debian.txt"
  - "support/bootstrap"
  - "support/bootstrap-common.sh"
  - "support/docker-build"
  - "support/gitlab-remote-development/Dockerfile.base"

.gitpod-code-changes: &gitpod-code-changes
  - "support/gitpod/**/*"
  - "support/ci/verify-gitpod-docker-image"
  - ".gitlab-ci.yml"
  - ".gitlab/ci/{build,deploy,verify}.gitlab-ci.yml"

.ruby-version-changes: &ruby-version-changes
  - ".ruby-version"
  - ".tool-versions"
  - "mise.toml"

########################
# Rules (Alphabetical) #
########################
.rules:build-integration-image:
  rules:
    - <<: *if-nightly
    - !reference [.rules:code-changes-verify, rules]

.rules:code-changes:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - changes: *code-changes

.rules:code-changes-mr-only:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - <<: *if-merge-request-expedited
      when: never
    - <<: *if-merge-request
      changes: *code-changes

.rules:code-changes-verify:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - <<: *if-merge-request-expedited
      when: never
    - changes: *code-changes

.rules:compile-binary:
  rules:
    - <<: *if-fork
      when: never
    - <<: *if-maintenance
    - <<: *if-merge-request-labels-run-compile-jobs
    - <<: *if-merge-request
      when: never

.rules:deploy-image:
  rules:
    - <<: *if-maintenance
      when: never
    - <<: *if-nightly

.rules:deploy-pages:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - <<: *if-default-branch-refs
      changes: *docs-code-changes

.rules:docs-changes:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - changes: *docs-changes

.rules:docs-code-changes:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - changes: *docs-code-changes

.rules:geo-install:
  rules:
    - <<: *if-fork
      when: never
    - !reference [.rules:code-changes-verify, rules]

.rules:gitlab-gdk-in-a-box-code-changes:
  rules:
    - <<: *if-maintenance
      when: never
    - <<: *if-nightly
    - <<: *if-merge-request-expedited
      when: never
    - <<: *if-merge-request
      changes: *gitlab-gdk-in-a-box-code-changes

.rules:gitlab-remote-code-changes:
  rules:
    - <<: *if-maintenance
      when: never
    - <<: *if-nightly
    - <<: *if-merge-request-expedited
      when: never
    - <<: *if-default-branch-refs
      changes: *gitlab-remote-workspace-base-changes
    - <<: *if-merge-request
      changes: *gitlab-remote-code-changes

.rules:gitlab-remote-workspace-base-changes:
  rules:
    - <<: *if-maintenance
      when: never
    - <<: *if-nightly
    - <<: *if-merge-request-expedited
      when: never
    - <<: *if-default-branch-refs
      changes: *gitlab-remote-workspace-base-changes
    - <<: *if-merge-request
      changes: *gitlab-remote-workspace-base-changes

.rules:gitpod-code-changes:
  rules:
    - <<: *if-maintenance
      when: never
    - <<: *if-nightly
    - <<: *if-merge-request-expedited
      when: never
    - <<: *if-merge-request
      changes: *gitpod-code-changes

.rules:novel:
  rules:
    - <<: *if-nightly
    - !reference [.skip-maintenance-and-nightly, rules]
    - changes: *code-changes
      when: manual
      allow_failure: true

.rules:packages-cleanup:
  rules:
    - <<: *if-fork
      when: never
    - <<: *if-maintenance
    - <<: *if-nightly
      when: never
    - <<: *if-merge-request
      when: never

.rules:postgres-upgrade:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - <<: *if-merge-request-expedited
      when: never
    - <<: *if-merge-request
      changes:
        - '.gitlab/ci/_versions.gitlab-ci.yml'
        - '.gitlab/ci/novel.gitlab-ci.yml'
        - .tool-versions

.rules:ruby-version-changes:
  rules:
    - !reference [.skip-maintenance-and-nightly, rules]
    - <<: *if-merge-request-expedited
      when: never
    - changes: *ruby-version-changes
