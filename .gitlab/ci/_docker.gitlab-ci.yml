.docker:build-docker-in-docker:
  image: ${GITLAB_DEPENDENCY_PROXY}docker:${DOCKER_VERSION}
  services:
    - name: docker:${DOCKER_VERSION}-dind
      # https://gitlab.com/gitlab-org/gitlab/-/issues/473739
      command: ["--ipv6=false", "--mtu=1400"]
  variables:
    DOCKER_VERSION: "27.5.0"
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  tags:
    # See https://gitlab.com/gitlab-com/www-gitlab-com/-/issues/7019 for tag descriptions
    - gitlab-org-docker
  before_script:
    - !reference [.default-before_script, before_script]
    - apk add --no-cache jq curl bash git
    - ./support/docker ci-login
  after_script:
    - ./support/docker ci-logout
