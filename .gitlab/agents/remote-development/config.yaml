observability:
  logging:
    level: debug
    grpc_level: warn

remote_development:
  enabled: true
  dns_zone: "workspaces.gitlab.dev"
  default_runtime_class: "sysbox-runc"
  allow_privilege_escalation: true
  annotations:
    "io.kubernetes.cri-o.userns-mode": "auto:size=65536"

  # See https://gitlab.com/groups/gitlab-org/quality/tooling/-/epics/69#risk-mitigation for a rationale of these restrictions
  default_resources_per_workspace_container:
    requests:
      cpu: "0.5"
      memory: "512Mi"
    limits:
      cpu: "0.5"
      memory: "768Mi"
