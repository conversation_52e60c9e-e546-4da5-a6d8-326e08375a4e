
observability:
  logging:
    level: debug
    grpc_level: warn
remote_development:
  enabled: true
  dns_zone: "gke-env-13b0ad4a.env-13b0ad4a.gcp.gitlabsandbox.net"
  allow_privilege_escalation: true
  use_kubernetes_user_namespaces: true
  # See https://gitlab.com/groups/gitlab-org/quality/tooling/-/epics/69#risk-mitigation for a rationale of these restrictions
  default_resources_per_workspace_container:
    requests:
      cpu: "0.5"
      memory: "512Mi"
    limits:
      cpu: "0.5"
      memory: "768Mi"
