PATH
  remote: gem
  specs:
    gitlab-development-kit (0.2.19)
      gitlab-sdk (~> 0.3.1)
      rake (~> 13.1)
      sentry-ruby (~> 5.23)
      terminal-table (~> 3.0.2)
      tty-markdown (~> 0.7.2)
      tty-spinner (~> 0.9.3)
      zeitwerk (~> 2.6.15)

GEM
  remote: https://rubygems.org/
  specs:
    activesupport (*******)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.2)
    base64 (0.2.0)
    bigdecimal (3.1.8)
    byebug (11.1.3)
    claide (1.1.0)
    claide-plugins (0.9.2)
      cork
      nap
      open4 (~> 1.3)
    coderay (1.1.3)
    colored2 (3.1.2)
    concurrent-ruby (1.3.3)
    connection_pool (2.4.1)
    cork (0.3.0)
      colored2 (~> 3.1)
    crack (1.0.0)
      bigdecimal
      rexml
    csv (3.3.0)
    danger (9.4.3)
      claide (~> 1.0)
      claide-plugins (>= 0.9.2)
      colored2 (~> 3.1)
      cork (~> 0.1)
      faraday (>= 0.9.0, < 3.0)
      faraday-http-cache (~> 2.0)
      git (~> 1.13)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.0)
      no_proxy_fix
      octokit (>= 4.0)
      terminal-table (>= 1, < 4)
    danger-gitlab (8.0.0)
      danger
      gitlab (~> 4.2, >= 4.2.0)
    diff-lcs (1.5.1)
    docile (1.4.0)
    drb (2.2.1)
    faraday (2.9.2)
      faraday-net_http (>= 2.0, < 3.2)
    faraday-http-cache (2.5.1)
      faraday (>= 0.8)
    faraday-net_http (3.1.0)
      net-http
    git (1.19.1)
      addressable (~> 2.8)
      rchardet (~> 1.8)
    gitlab (4.20.1)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    gitlab-dangerfiles (4.8.1)
      danger (>= 9.3.0)
      danger-gitlab (>= 8.0.0)
      rake (~> 13.0)
    gitlab-sdk (0.3.1)
      activesupport (>= 5.2.0)
      rake (~> 13.0)
      snowplow-tracker (~> 0.8.0)
    gitlab-styles (13.0.2)
      rubocop (~> 1.68.0)
      rubocop-capybara (~> 2.21.0)
      rubocop-factory_bot (~> 2.26.1)
      rubocop-graphql (~> 1.5.4)
      rubocop-performance (~> 1.21.1)
      rubocop-rails (~> 2.26.0)
      rubocop-rspec (~> 3.0.4)
      rubocop-rspec_rails (~> 2.30.0)
    hashdiff (1.1.1)
    httparty (0.22.0)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    io-console (0.7.2)
    irb (1.15.1)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.7.2)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (3.17.0.3)
    lefthook (1.10.10)
    logger (1.6.6)
    method_source (1.0.0)
    mini_mime (1.1.5)
    minitest (5.24.1)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    mutex_m (0.2.0)
    nap (1.1.0)
    net-http (0.4.1)
      uri
    no_proxy_fix (0.1.2)
    octokit (6.1.1)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    open4 (1.3.4)
    parallel (1.25.1)
    parser (3.3.3.0)
      ast (~> 2.4.1)
      racc
    pastel (0.8.0)
      tty-color (~> 0.5)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.3.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    psych (5.1.2)
      stringio
    public_suffix (5.1.1)
    racc (1.8.0)
    rack (3.1.8)
    rainbow (3.1.1)
    rake (13.2.1)
    rbs (3.8.1)
      logger
    rchardet (1.8.0)
    rdoc (6.6.3.1)
      psych (>= 4.0.0)
    regexp_parser (2.9.2)
    reline (0.5.9)
      io-console (~> 0.5)
    resolv (0.6.0)
    rexml (3.3.1)
      strscan
    rouge (4.4.0)
    rspec (3.13.0)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.0)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.68.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.4, < 3.0)
      rubocop-ast (>= 1.32.2, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.32.3)
      parser (>= 3.3.1.0)
    rubocop-capybara (2.21.0)
      rubocop (~> 1.41)
    rubocop-factory_bot (2.26.1)
      rubocop (~> 1.61)
    rubocop-graphql (1.5.4)
      rubocop (>= 1.50, < 2)
    rubocop-performance (1.21.1)
      rubocop (>= 1.48.1, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rails (2.26.2)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.52.0, < 2.0)
      rubocop-ast (>= 1.31.1, < 2.0)
    rubocop-rake (0.6.0)
      rubocop (~> 1.0)
    rubocop-rspec (3.0.5)
      rubocop (~> 1.61)
    rubocop-rspec_rails (2.30.0)
      rubocop (~> 1.61)
      rubocop-rspec (~> 3, >= 3.0.1)
    ruby-lsp (0.23.11)
      language_server-protocol (~> 3.17.0)
      prism (>= 1.2, < 2.0)
      rbs (>= 3, < 4)
      sorbet-runtime (>= 0.5.10782)
    ruby-lsp-rspec (0.1.22)
      ruby-lsp (~> 0.23.0)
    ruby-progressbar (1.13.0)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    sentry-ruby (5.23.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    simplecov (0.21.2)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-cobertura (3.0.0)
      rexml
      simplecov (~> 0.19)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    snowplow-tracker (0.8.0)
    sorbet-runtime (0.5.11911)
    stringio (3.1.1)
    strings (0.2.1)
      strings-ansi (~> 0.2)
      unicode-display_width (>= 1.5, < 3.0)
      unicode_utils (~> 1.4)
    strings-ansi (0.2.0)
    strscan (3.1.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    tty-color (0.6.0)
    tty-cursor (0.7.1)
    tty-markdown (0.7.2)
      kramdown (>= 1.16.2, < 3.0)
      pastel (~> 0.8)
      rouge (>= 3.14, < 5.0)
      strings (~> 0.2.0)
      tty-color (~> 0.5)
      tty-screen (~> 0.8)
    tty-screen (0.8.2)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    unicode_utils (1.4.0)
    uri (0.13.0)
    webmock (3.25.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    yard (0.9.37)
    zeitwerk (2.6.15)

PLATFORMS
  ruby

DEPENDENCIES
  gitlab-dangerfiles (~> 4.8.1)
  gitlab-development-kit!
  gitlab-styles (~> 13.0.2)
  irb (~> 1.15.1)
  lefthook (~> 1.10.10)
  pry-byebug
  resolv (~> 0.6.0)
  rspec (~> 3.13.0)
  rspec_junit_formatter (~> 0.6.0)
  rubocop
  rubocop-rake (~> 0.6.0)
  ruby-lsp (~> 0.23.0)
  ruby-lsp-rspec (~> 0.1.10)
  simplecov-cobertura (~> 3.0.0)
  webmock (~> 3.25)
  yard (~> 0.9.37)

BUNDLED WITH
   2.6.5
