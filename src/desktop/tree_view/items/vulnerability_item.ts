import { TreeItem, Uri } from 'vscode';
import * as vscode from 'vscode';
import { VS_COMMANDS } from '../../../common/command_names';

export class VulnerabilityItem extends TreeItem {
  constructor(v: RestVulnerability) {
    super(`[${v.severity}] - ${v.name}`);
    const arg = v.location
      ? Uri.file(`${vscode.workspace.rootPath}/${v.location.file}`)
      : Uri.parse(v.web_url);
    this.command = {
      title: 'Open Vulnerability',
      command: VS_COMMANDS.OPEN,
      arguments: [arg],
    };
  }
}
