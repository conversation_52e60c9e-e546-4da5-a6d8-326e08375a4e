import * as path from 'path';
import * as fs from 'fs/promises';
import * as os from 'os';
import * as vscode from 'vscode';

const DEFAULT_CONFIG = {
  mcpServers: {},
};

/**
 * Gets the cross-platform path to the MCP config file
 * @returns The absolute path to the MCP config file
 */
export function getMcpUserConfigPath(): string {
  const homeDir = os.homedir();
  if (!homeDir || homeDir.trim() === '') {
    throw new Error('Unable to determine home directory. Please set HOME environment variable.');
  }
  const configDir = path.join(homeDir, '.gitlab', 'duo');

  return path.join(configDir, 'mcp.json');
}

/**
 * Creates a default MCP config file if it doesn't exist
 * @returns Promise that resolves when config file is created or already exists
 */
export async function ensureMcpUserConfigFile(): Promise<void> {
  const configPath = getMcpUserConfigPath();
  const configDir = path.dirname(configPath);
  const defaultConfigJson = JSON.stringify(DEFAULT_CONFIG, null, 2);

  await fs.mkdir(configDir, { recursive: true });

  try {
    await fs.writeFile(configPath, defaultConfigJson, { flag: 'wx', encoding: 'utf8' });
  } catch (err: unknown) {
    if (err && typeof err === 'object' && 'code' in err && err.code === 'EEXIST') {
      const stats = await fs.stat(configPath);
      if (stats.isDirectory()) {
        throw new Error(
          `Config path exists but is a directory: ${configPath}. Please remove or rename it.`,
        );
      }

      return;
    }

    // Other errors (EACCES, ENOSPC, etc.) should be propagated
    throw err;
  }
}

/**
 * Opens the MCP config file in VS Code
 * @returns Promise that resolves when the file is opened
 */
export async function openMcpUserConfigFile(): Promise<void> {
  await ensureMcpUserConfigFile();
  const configPath = getMcpUserConfigPath();
  const uri = vscode.Uri.file(configPath);

  await vscode.commands.executeCommand('vscode.open', uri);
}
