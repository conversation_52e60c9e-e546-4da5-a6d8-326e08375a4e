import crossFetch from 'cross-fetch';
import { log } from './log';
import { extractURL } from './utils/extract_url';

async function fetchLogged(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
  const start = Date.now();
  const url = extractURL(input);
  const method = init?.method || 'GET';
  const headers = init?.headers || {};
  const verboseLogging = process.env.VSCODE_GITLAB_VERBOSE_LOGGING === 'true';

  // Log request details if verbose logging is enabled
  if (verboseLogging) {
    log.debug(`fetch: ${method} ${url}`);
    log.debug(`fetch: headers: ${JSON.stringify(headers)}`);
    if (init?.body && typeof init.body === 'string') {
      try {
        const bodyObj = JSON.parse(init.body);
        log.debug(`fetch: request body: ${JSON.stringify(bodyObj)}`);
      } catch {
        log.debug(`fetch: request body (raw): ${init.body}`);
      }
    }
  }

  try {
    const resp = await crossFetch(input, init);
    const duration = Date.now() - start;

    log.debug(`fetch: request to ${url} returned HTTP ${resp.status} after ${duration} ms`);

    // Log response details if verbose logging is enabled
    if (verboseLogging) {
      const responseHeaders: Record<string, string> = {};
      resp.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });
      log.debug(`fetch: response headers: ${JSON.stringify(responseHeaders)}`);

      // Clone response to read body without consuming it
      const respClone = resp.clone();
      try {
        const responseText = await respClone.text();
        if (responseText) {
          try {
            const responseObj = JSON.parse(responseText);
            log.debug(`fetch: response body: ${JSON.stringify(responseObj)}`);
          } catch {
            const truncatedText = responseText.substring(0, 500) + (responseText.length > 500 ? '...' : '');
            log.debug(`fetch: response body (raw): ${truncatedText}`);
          }
        }
      } catch (bodyError) {
        log.debug(`fetch: could not read response body: ${bodyError instanceof Error ? bodyError.message : String(bodyError)}`);
      }
    }

    return resp;
  } catch (e) {
    const duration = Date.now() - start;
    log.debug(`fetch: request to ${url} threw an exception after ${duration} ms`);
    log.debug(`fetch: request to ${url} failed with:`, e);

    throw e;
  }
}

// eslint-disable-next-line import/no-default-export
export default fetchLogged;
