import * as vscode from 'vscode';
import { ApplyWorkspaceEditParams, ApplyWorkspaceEditResult } from 'vscode-languageclient';
import { createFakePartial } from '../test_utils/create_fake_partial';
import { getLocalFeatureFlagService } from '../feature_flags/local_feature_flag_service';
import { DiffMiddleware } from './diff_middleware';
import { FileSnapshotProvider } from './file_snapshot_provider';

jest.mock('../feature_flags/local_feature_flag_service');

describe('DiffMiddleware', () => {
  let middleware: DiffMiddleware;
  let mockFileSnapshotProvider: FileSnapshotProvider;
  let mockNext: jest.Mock;

  const mockFileUri = vscode.Uri.parse('file:///test/file.ts');
  const mockSnapshotUri = vscode.Uri.parse('gitlab-snapshot:file-snapshot.ts');

  const createTestParams = (): ApplyWorkspaceEditParams =>
    createFakePartial<ApplyWorkspaceEditParams>({
      edit: {
        documentChanges: [
          {
            textDocument: { uri: mockFileUri.toString(), version: 1 },
            edits: [
              {
                range: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
                newText: 'test',
              },
            ],
          },
        ],
      },
    });

  beforeEach(() => {
    mockFileSnapshotProvider = createFakePartial<FileSnapshotProvider>({
      takeSnapshot: jest.fn().mockResolvedValue({ dispose: jest.fn() }),
      hasContent: jest.fn().mockReturnValue(true),
      snapshotUri: jest.fn().mockReturnValue(mockSnapshotUri),
    });

    middleware = new DiffMiddleware(mockFileSnapshotProvider);

    mockNext = jest.fn().mockResolvedValue(
      createFakePartial<ApplyWorkspaceEditResult>({
        applied: true,
      }),
    );

    vscode.commands.executeCommand = jest.fn().mockResolvedValue(undefined);

    jest.mocked(getLocalFeatureFlagService).mockReturnValue(
      createFakePartial({
        isEnabled: jest.fn().mockReturnValue(true),
      }),
    );
  });

  it('should take snapshots, apply edits, and show diffs for edited files', async () => {
    const params = createTestParams();

    const result = await middleware.process(params, mockNext);

    // Verify snapshots were taken
    expect(mockFileSnapshotProvider.takeSnapshot).toHaveBeenCalledWith(mockFileUri);

    // Verify next middleware was called
    expect(mockNext).toHaveBeenCalledWith(params);

    // Verify diff view was opened
    expect(mockFileSnapshotProvider.hasContent).toHaveBeenCalledWith(mockFileUri);
    expect(mockFileSnapshotProvider.snapshotUri).toHaveBeenCalledWith(mockFileUri);
    expect(vscode.commands.executeCommand).toHaveBeenCalledWith(
      'vscode.diff',
      mockSnapshotUri,
      mockFileUri,
      'file.ts: Original ↔ Edited',
    );

    // Verify result is returned
    expect(result).toEqual({ applied: true });
  });

  it('should dispose snapshots after processing', async () => {
    const mockDispose = jest.fn();
    jest.mocked(mockFileSnapshotProvider.takeSnapshot).mockResolvedValue({ dispose: mockDispose });

    const params = createTestParams();

    await middleware.process(params, mockNext);

    // Verify dispose was called on the returned disposable
    expect(mockDispose).toHaveBeenCalled();
  });

  it('should bypass diff logic when feature flag is disabled', async () => {
    jest.mocked(getLocalFeatureFlagService).mockReturnValue(
      createFakePartial({
        isEnabled: jest.fn().mockReturnValue(false),
      }),
    );

    const params = createFakePartial<ApplyWorkspaceEditParams>({
      edit: { documentChanges: [] },
    });

    const result = await middleware.process(params, mockNext);

    // Verify no snapshots were taken
    expect(mockFileSnapshotProvider.takeSnapshot).not.toHaveBeenCalled();

    // Verify next middleware was still called
    expect(mockNext).toHaveBeenCalledWith(params);

    // Verify no diff views were opened
    expect(vscode.commands.executeCommand).not.toHaveBeenCalled();

    expect(result).toEqual({ applied: true });
  });
});
