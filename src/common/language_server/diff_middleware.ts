import * as vscode from 'vscode';
import {
  ApplyWorkspaceEditParams,
  ApplyWorkspaceEditResult,
  TextDocumentEdit,
  WorkspaceEdit,
} from 'vscode-languageclient';
import { createConverter } from 'vscode-languageclient/lib/common/protocolConverter';
import { log } from '../log';
import { VS_COMMANDS } from '../command_names';
import { ApplyEditMiddleware } from './apply_edit_client_wrapper';
import { FileSnapshotProvider } from './file_snapshot_provider';

export class DiffMiddleware implements ApplyEditMiddleware {
  #p2c = createConverter(undefined, false, false);

  #fileSnapshotProvider: FileSnapshotProvider;

  constructor(fileSnapshotProvider: FileSnapshotProvider) {
    this.#fileSnapshotProvider = fileSnapshotProvider;
  }

  async process(
    params: ApplyWorkspaceEditParams,
    next: (params: ApplyWorkspaceEditParams) => Promise<ApplyWorkspaceEditResult>,
  ): Promise<ApplyWorkspaceEditResult> {
    const editedUris = this.#extractEditedUris(params.edit);
    const snapshotDisposables = await this.#takeSnapshots(editedUris);

    const result = await next(params);

    if (result.applied) {
      await this.#showDiffForEditedFiles(editedUris);
    }

    // we clear all recorded snapshots after we process the edits
    // this is OK because the VS Code only asks the provider once for the URI content and it doesn't ask again unless we close the tab
    vscode.Disposable.from(...snapshotDisposables).dispose();
    return result;
  }

  #extractEditedUris(workspaceEdit: WorkspaceEdit): vscode.Uri[] {
    if (!workspaceEdit.documentChanges) return [];

    return workspaceEdit.documentChanges
      .filter(TextDocumentEdit.is)
      .map(change => this.#p2c.asUri(change.textDocument.uri));
  }

  async #takeSnapshots(uris: vscode.Uri[]): Promise<vscode.Disposable[]> {
    return Promise.all(
      uris.map(async uri => {
        try {
          return await this.#fileSnapshotProvider.takeSnapshot(uri);
        } catch (e) {
          log.error(
            `[DiffMiddleware] Failed to take a snapshot for ${uri.fsPath}. Will use empty content.`,
            e,
          );
          return { dispose() {} };
        }
      }),
    );
  }

  async #showDiffForEditedFiles(uris: vscode.Uri[]): Promise<void> {
    await Promise.all(
      uris
        .filter(uri => this.#fileSnapshotProvider.hasContent(uri))
        .map(async uri => {
          try {
            await this.#openDiffView(uri);
          } catch (e) {
            log.error(`[DiffMiddleware] Failed to show diff for ${uri.fsPath}.`, e);
          }
        }),
    );
  }

  async #openDiffView(fileUri: vscode.Uri): Promise<void> {
    const fileName = fileUri.path.split('/').pop() || 'untitled';
    const snapshotUri = this.#fileSnapshotProvider.snapshotUri(fileUri);
    const diffTitle = `${fileName}: Original ↔ Edited`;

    await vscode.commands.executeCommand(VS_COMMANDS.DIFF, snapshotUri, fileUri, diffTitle);

    log.debug(`[DiffMiddleware] Opened diff view: "${diffTitle}"`);
  }
}
