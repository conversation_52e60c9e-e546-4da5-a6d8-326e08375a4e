{"name": "vue-webviews", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "issuable": "vite --config issuable/vite.config.js build", "issuable-watch": "vite --config issuable/vite.config.js build --watch", "build": "rimraf dist/ && concurrently 'npm:issuable'", "watch": "rimraf dist/ && concurrently 'npm:issuable-watch'", "test": "vitest", "preview": "vite preview", "lint:eslint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --ignore-path .gitignore", "lint:prettier": "prettier --check **/src/", "lint": "npm run lint:eslint && npm run lint:prettier", "autofix": "npm run lint:eslint -- --fix && npm run format", "format": "prettier --write **/src/", "preinstall": "node ../../scripts/ensure_npm.mjs"}, "dependencies": {"@vue/compat": "^3.3.4", "dompurify": "^3.0.5", "marked": "^5.1.2", "marked-bidi": "^1.0.3", "v-tooltip": "^2.1.3", "vue": "^3.3.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "@vue/test-utils": "^2.4.0", "concurrently": "^8.2.0", "dayjs": "^1.11.8", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "jsdom": "^22.1.0", "markdown-it": "^13.0.1", "markdown-it-checkbox": "^1.1.0", "prettier": "^2.8.8", "rimraf": "^5.0.5", "sass": "^1.63.6", "sass-loader": "^13.3.2", "vite": "^4.3.9", "vitest": "^0.32.2"}}