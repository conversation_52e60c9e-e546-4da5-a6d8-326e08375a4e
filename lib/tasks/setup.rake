# frozen_string_literal: true

desc 'Preflight checks for dependencies'
task 'preflight-checks' do
  checker = GDK::Dependencies::Checker.new(preflight: true)
  checker.check_all

  unless checker.error_messages.empty?
    messages = checker.error_messages.map { |m| m.nil? ? "" : m.dup.prepend('- ') }.join("\n")
    fix = if Utils.executable_exist?('brew')
            "To fix this, run: cd #{GDK.config.gdk_root} && brew bundle install"
          else
            "To fix this, see https://gitlab-org.gitlab.io/gitlab-development-kit/advanced/."
          end

    raise GDK::UserInteractionRequired, "Missing software needed for GDK:\n\n#{messages}\n\n#{fix}"
  end
end

desc 'Preflight Update checks'
task 'preflight-update-checks' do
  postgresql = GDK::Postgresql.new
  if postgresql.installed? && postgresql.upgrade_needed?
    message = <<~MESSAGE
      PostgreSQL data directory is version #{postgresql.current_version} and must be upgraded to version #{postgresql.class.target_version} before GDK can be updated.
    MESSAGE

    GDK::Output.warn(message)

    if ENV['PG_AUTO_UPDATE']
      GDK::Output.warn('PostgreSQL will be auto-updated in 10 seconds. Hit CTRL-C to abort.')
      Kernel.sleep 10
    else
      prompt_response = GDK::Output.prompt("This will run 'support/upgrade-postgresql' to back up and upgrade the PostgreSQL data directory. Are you sure? [y/N]").match?(/\Ay(?:es)*\z/i)
      next unless prompt_response
    end

    postgresql.upgrade

    GDK::Output.success("Successfully ran 'support/upgrade-postgresql' script!")
  end
end

namespace :update do
  desc 'Tool versions update'
  task 'tool-versions' do |t|
    t.skip! unless GDK.config.tool_version_manager.enabled?
    GDK::ToolVersionsUpdater.new.run
  end
end
