# frozen_string_literal: true

namespace :object_store do
  desc 'Setup Object Store default buckets'
  task :setup do
    next unless GDK.config.object_store.enabled?

    data_dir = GDK::Services::Minio.new.data_dir
    GDK.config.object_store.objects.each do |key, data|
      bucket = data['bucket']
      raise GDK::UserInteractionRequired, "Expected a `bucket` name for object_store.objects.#{key} in gdk.yml." if bucket.nil? || bucket.empty?

      bucket_directory = data_dir.join(bucket)

      bucket_directory.mkpath unless bucket_directory.exist?
    end
  end
end
