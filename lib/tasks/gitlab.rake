# frozen_string_literal: true

namespace :gitlab do
  desc 'GitLab: Truncate logs'
  task :truncate_logs, [:prompt] do |_, args|
    if args[:prompt] != 'false'
      GDK::Output.warn('About to truncate gitlab/log/* files.')
      GDK::Output.puts(stderr: true)

      next unless GDK::Output.interactive?

      prompt_response = GDK::Output.prompt('Are you sure? [y/N]').match?(/\Ay(?:es)*\z/i)
      next unless prompt_response

      GDK::Output.puts(stderr: true)
    end

    result = GDK.config.gitlab.log_dir.glob('*').map { |file| file.truncate(0) }.all?(0)
    raise 'Truncation of gitlab/log/* files failed.' unless result

    GDK::Output.success('Truncated gitlab/log/* files.')
  end

  desc 'GitLab: Truncate http router logs'
  task :truncate_http_router_logs, [:prompt] do |_, args|
    if args[:prompt] != 'false'
      GDK::Output.warn("About to truncate #{GDK::Services::GitlabHttpRouter::LOG_PATH} file.")
      GDK::Output.puts(stderr: true)

      next unless GDK::Output.interactive?

      prompt_response = GDK::Output.prompt('Are you sure? [y/N]').match?(/\Ay(?:es)*\z/i)
      next unless prompt_response

      GDK::Output.puts(stderr: true)
    end

    http_router_log_file = GDK.config.gdk_root.join(GDK::Services::GitlabHttpRouter::LOG_PATH)
    next unless http_router_log_file.exist?

    result = http_router_log_file.truncate(0).zero?
    raise "Truncation of #{GDK::Services::GitlabHttpRouter::LOG_PATH} file failed." unless result

    GDK::Output.success("Truncated #{GDK::Services::GitlabHttpRouter::LOG_PATH} file.")
  end

  desc 'GitLab: Recompile translations'
  task :********************** do
    task = GDK::Execute::Rake.new('gettext:compile')
    state = task.execute_in_gitlab(display_output: false)

    # Log rake output to ${gitlab_dir}/log/gettext.log
    GDK.config.gitlab.log_dir.join('gettext.log').open('w') do |file|
      file.write(state.output)
    end

    state.success?
  end
end
