# frozen_string_literal: true

namespace :gdk do
  migrations = %w[
    migrate:update_telemetry_settings
    migrate:mise
    migrate:mise_trust
  ]

  desc 'Run migration related to GDK setup'
  task migrate: migrations

  namespace :migrate do
    desc 'Update settings to turn on telemetry for GitLab team members (determined by @gitlab.com email in git config) and anonymize usernames for all users'
    task :update_telemetry_settings do
      telemetry_enabled = GDK.config.telemetry.enabled
      is_team_member = GDK::Telemetry.team_member?
      should_update = telemetry_enabled || is_team_member

      if should_update
        GDK::Output.info('Telemetry has been automatically enabled for you as a GitLab team member.') if !telemetry_enabled && is_team_member

        GDK::Telemetry.update_settings('y')
      end
    end

    desc 'Prompts GitLab team members to migrate from asdf to mise if asdf is still in use'
    task :mise do
      next unless GDK::Telemetry.team_member?
      next if GDK.config.asdf.opt_out? || GDK.config.tool_version_manager.enabled?
      next unless GDK::Remind<PERSON><PERSON><PERSON><PERSON>.should_run_reminder?('mise_migration')

      update_message = <<~MESSAGE
        We're no longer supporting asdf in GDK.

        You can still use asdf if you need to, for example outside of GDK. But it's no longer supported in GDK and won't be maintained going forward.

        Mise provides better supply chain security while running faster and avoiding the dependency installation problems that we had to manually fix with asdf.

        To migrate, run:
          gdk update
      MESSAGE

      GDK::Output.warn(update_message)
      GDK::Output.puts

      unless GDK::Output.interactive?
        GDK::Output.info('Skipping mise migration prompt in non-interactive environment.')
        next
      end

      if GDK::Output.prompt('Would you like it to switch to mise now? [y/N]').match?(/\Ay(?:es)*\z/i)
        GDK::Output.info('Great! Running the mise migration now..')
        GDK::Execute::Rake.new('mise:migrate').execute_in_gdk
      else
        GDK::Output.info("No worries. We'll remind you again in 5 days.")
        GDK::ReminderHelper.update_reminder_timestamp!('mise_migration')
      end
    end

    desc 'Trust the .mise.toml configuration'
    task :mise_trust do
      next unless GDK.config.tool_version_manager.enabled?

      cache_file = File.join(GDK.config.gdk_root, '.cache', '.mise_trusted')
      next if File.exist?(cache_file)

      mise_config = File.join(GDK.config.gdk_root, '.mise.toml')
      sh = GDK::Shellout.new("mise trust #{mise_config}").execute
      if sh.success?
        FileUtils.mkdir_p(File.dirname(cache_file))
        FileUtils.touch(cache_file)
      end
    end
  end
end
