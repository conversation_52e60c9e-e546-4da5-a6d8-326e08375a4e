# frozen_string_literal: true

module GDK
  module Command
    class SendTelemetry < BaseCommand
      def run(args = [])
        metric, value, *extra_args = args

        raise UserInteractionRequired, 'Usage: gdk send-telemetry <metric> <value>' unless metric && value

        extras = parse_extra_args(extra_args)

        GDK::Telemetry.send_custom_event(metric, value, extras: extras)
        GDK::Output.debug("Telemetry event sent: metric=#{metric}, value=#{value}, extras=#{extras}")
        true
      end

      private

      def parse_extra_args(args)
        args.each_with_object({}) do |arg, hash|
          next unless arg.start_with?('--extra=')

          key, val = arg.sub('--extra=', '').split(':', 2)
          if key && !key.strip.empty? && val && !val.strip.empty?
            hash[key] = val
          else
            GDK::Output.warn("Invalid --extra format: #{arg}")
          end
        end
      end
    end
  end
end
