# frozen_string_literal: true

module GDK
  module Command
    # Executes clickhouse client command with configured connection paras and any provided extra arguments
    class Clickhouse < BaseCommand
      def run(args = [])
        unless GDK.config.clickhouse.enabled?
          raise UserInteractionRequired.new(
            'ClickHouse is not enabled.',
            docs: 'https://docs.gitlab.com/development/database/clickhouse/clickhouse_within_gitlab/'
          )
        end

        exec(*command(args), chdir: GDK.root)
      end

      private

      def command(args = [])
        clickhouse = config.clickhouse

        base = %W[#{clickhouse.bin} client --port=#{clickhouse.tcp_port}]
        (base + args).flatten
      end
    end
  end
end
