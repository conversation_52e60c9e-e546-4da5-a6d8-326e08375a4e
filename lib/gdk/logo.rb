# frozen_string_literal: true

module GDK
  module Lo<PERSON>
    def self.print
      logo = <<-LOGO
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;28;8;5m [0m[38;2;113;34;21m.[0m[38;2;31;9;6m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;4;1;1m [0m[38;2;113;33;21m.[0m[38;2;77;23;14m.[0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;21;6;4m [0m[38;2;215;64;39m:[0m[38;2;226;67;41mc[0m[38;2;215;64;39m:[0m[38;2;23;7;4m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;165;49;30m,[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;140;41;25m'[0m[38;2;5;5;5m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;22;7;4m [0m[38;2;210;62;38m:[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;210;62;38m:[0m[38;2;10;3;2m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;136;41;25m'[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;97;28;17m.[0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;4;1;1m [0m[38;2;204;61;37m:[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;165;49;30m,[0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;60;18;11m [0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;38;11;7m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;119;36;22m.[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;44;13;8m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;4;1;1m [0m[38;2;214;63;39m:[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;201;59;36m:[0m[38;2;4;1;1m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;50;15;9m [0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;205;60;37m:[0m[38;2;7;2;1m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;119;36;22m.[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;148;44;26m'[0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;36;11;7m [0m[38;2;215;64;39m:[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;193;57;35m;[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;129;38;23m.[0m[38;2;145;43;26m'[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;145;43;26m'[0m[38;2;5;5;5m [0m
  [0m[38;2;26;9;4m [0m[38;2;229;73;40mc[0m[38;2;227;69;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;68;41mc[0m[38;2;229;71;41mc[0m[38;2;115;40;19m'[0m
  [0m[38;2;188;81;28m:[0m[38;2;252;108;38mo[0m[38;2;249;104;38mo[0m[38;2;244;97;39mo[0m[38;2;239;88;39ml[0m[38;2;234;80;40mc[0m[38;2;228;70;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;227;68;41mc[0m[38;2;231;76;40mc[0m[38;2;237;85;40ml[0m[38;2;243;94;39ml[0m[38;2;247;102;38mo[0m[38;2;251;107;38mo[0m[38;2;252;109;38mo[0m
  [0m[38;2;247;107;37mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;250;105;38mo[0m[38;2;243;94;39ml[0m[38;2;235;81;40ml[0m[38;2;228;70;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;232;76;40mc[0m[38;2;240;90;39ml[0m[38;2;248;102;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m
  [0m[38;2;155;67;23m;[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;250;106;38mo[0m[38;2;243;94;39ml[0m[38;2;234;81;40ml[0m[38;2;228;70;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;232;76;40mc[0m[38;2;240;90;39ml[0m[38;2;247;101;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;241;104;36ml[0m
  [0m[38;2;3;1;0m [0m[38;2;244;106;37mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;249;105;38mo[0m[38;2;241;91;39ml[0m[38;2;231;76;40mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;228;71;41mc[0m[38;2;238;86;40ml[0m[38;2;246;99;39mo[0m[38;2;252;108;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;70;30;10m.[0m
  [0m[38;2;7;7;7m [0m[38;2;61;61;61m [0m[38;2;67;29;10m.[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;251;108;38mo[0m[38;2;244;97;39mo[0m[38;2;237;85;40ml[0m[38;2;230;74;40mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;226;67;41mc[0m[38;2;228;70;41mc[0m[38;2;235;81;40ml[0m[38;2;242;92;39ml[0m[38;2;250;105;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;162;70;24m;[0m[38;2;92;92;92m [0m[38;2;20;20;20m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;21;21;21m [0m[38;2;75;75;75m [0m[38;2;9;4;1m [0m[38;2;205;89;31mc[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;251;108;38mo[0m[38;2;244;96;39mo[0m[38;2;236;87;40ml[0m[38;2;234;87;40ml[0m[38;2;241;92;39ml[0m[38;2;250;105;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;55;24;8m.[0m[38;2;89;89;89m [0m[38;2;41;41;41m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;7;7;7m [0m[38;2;31;31;31m [0m[38;2;75;75;75m [0m[38;2;5;2;1m [0m[38;2;221;95;33ml[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;110;38mo[0m[38;2;252;123;38md[0m[38;2;252;141;38mx[0m[38;2;252;158;38mk[0m[38;2;252;161;38mk[0m[38;2;252;146;38mx[0m[38;2;252;129;38md[0m[38;2;252;115;38md[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;55;24;8m.[0m[38;2;89;89;89m [0m[38;2;51;51;51m [0m[38;2;14;14;14m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;8;8;8m [0m[38;2;48;48;48m [0m[38;2;96;96;96m [0m[38;2;79;34;12m.[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;117;38md[0m[38;2;252;137;38mx[0m[38;2;252;154;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;160;38mk[0m[38;2;252;143;38mx[0m[38;2;252;123;38md[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;252;109;38mo[0m[38;2;173;75;26m;[0m[38;2;107;107;107m [0m[38;2;72;72;72m [0m[38;2;16;16;16m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;21;21;21m [0m[38;2;61;61;61m [0m[38;2;105;105;105m [0m[38;2;80;45;12m.[0m[38;2;252;160;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;168;99;25m:[0m[38;2;121;121;121m [0m[38;2;82;82;82m [0m[38;2;34;34;34m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;25;25;25m [0m[38;2;75;75;75m [0m[38;2;133;133;133m [0m[38;2;134;86;20m;[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;252;163;38mk[0m[38;2;236;153;36mx[0m[38;2;14;9;2m [0m[38;2;100;100;100m [0m[38;2;41;41;41m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;37;37;37m [0m[38;2;75;75;75m [0m[38;2;133;133;133m [0m[38;2;157;102;24m:[0m[38;2;212;137;32md[0m[38;2;18;12;3m [0m[38;2;100;100;100m [0m[38;2;50;50;50m [0m[38;2;8;8;8m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
  [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;58;58;58m [0m[38;2;58;58;58m [0m[38;2;19;19;19m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m[38;2;0;0;0m [0m
      LOGO

      return GDK::Output.puts logo if GDK::Output.colorize?

      GDK::Output.puts logo.gsub(/\e[^m]+m/, '')
    end
  end
end
