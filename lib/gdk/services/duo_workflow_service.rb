# frozen_string_literal: true

module GDK
  module Services
    class DuoWorkflowService < Base
      def name
        '********************'
      end

      def command
        'support/exec-cd gitlab-ai-gateway poetry run ********************'
      end

      def env
        { 'PORT' => config.duo_workflow.port }
      end

      def enabled?
        config.duo_workflow?
      end
    end
  end
end
