# frozen_string_literal: true

module GDK
  module Services
    class GitlabK8sAgent < Base
      def name
        'gitlab-k8s-agent'
      end

      def command
        %(#{config.gitlab_k8s_agent.__command} --configuration-file '#{config.gitlab_k8s_agent.__config_file}')
      end

      def env
        { 'OWN_PRIVATE_API_URL' => config.gitlab_k8s_agent.__private_api_url }
      end

      def ready_message
        <<~MESSAGE
          GitLab Agent Server (KAS) available at #{config.gitlab_k8s_agent.__url_for_agentk}.
          Kubernetes proxy (via KAS) available at #{config.gitlab_k8s_agent.__k8s_api_url}.
        MESSAGE
      end

      def enabled?
        config.gitlab_k8s_agent? && !config.gitlab_k8s_agent.configure_only
      end
    end
  end
end
