# frozen_string_literal: true

module GDK
  module Predictive
    class Base
      def out
        GDK::Output
      end

      def all_changed_files
        @all_changed_files ||= "#{shellout('git diff --name-only -z', chdir: gitlab_dir)}\0" \
          "#{shellout('git diff origin/master...HEAD --name-only -z', chdir: gitlab_dir)}"
          .split("\0")
          .reject(&:empty?)
          .uniq
      end

      def gitlab_dir
        @gitlab_dir ||= GDK.config.gitlab.dir
      end

      def shellout(cmd, **args)
        Shellout.new(cmd, **args).run
      rescue StandardError => e
        raise "Failed to execute shell command: #{e.message}"
      end
    end
  end
end
