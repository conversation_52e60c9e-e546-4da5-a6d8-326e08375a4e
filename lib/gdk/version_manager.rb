# frozen_string_literal: true

module GDK
  class VersionManager
    VERSION_FILES = {
      gitaly: 'GITALY_SERVER_VERSION',
      gitlab_shell: 'GITLAB_SHELL_VERSION',
      workhorse: 'GITLAB_WORKHORSE_VERSION'
    }.freeze

    DEFAULT_VERSIONS = {
      gitaly: 'main',
      gitlab_shell: 'main',
      workhorse: 'main',
      graphql_schema: 'master'
    }.freeze

    def self.fetch(name)
      return DEFAULT_VERSIONS.fetch(name, 'main') unless VERSION_FILES.key?(name)

      filename = VERSION_FILES[name]
      version_path = GDK.config.gdk_root.join('gitlab', filename)

      File.exist?(version_path) ? File.read(version_path).strip : DEFAULT_VERSIONS[name]
    end
  end
end
