# frozen_string_literal: true

require 'base64'

module GD<PERSON>
  # Reads the version file and returns its version or an empty string if it doesn't exist.
  module ConfigHelper
    extend self

    def create_encoded_subject_dn(common_name)
      name = OpenSSL::X509::Name.new([['CN', common_name]])
      der_bytes = name.to_der
      Base64.strict_encode64(der_bytes)
    end

    def version_from(config, path)
      full_path = config.gdk_root.join(path)
      return '' unless full_path.exist?

      version = full_path.read.chomp
      process_version(version)
    end

    private

    def process_version(version)
      # Returns commit hash as is
      return version if version.length == 40

      "v#{version}"
    end
  end
end
