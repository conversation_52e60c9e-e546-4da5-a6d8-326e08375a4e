#!/usr/bin/env bash

ERROR_CODE_ASDF_SHIM_BINARY_NOT_FOUND=126

debug() {
  local msg="$1"

  if [[ $GDK_DEBUG == 1 || $GDK_DEBUG == true ]]; then
    echo "DEBUG: $msg"
  fi
}

find_gdk_root() {
  if [[ "${1}" == "/" ]]; then
    exit 1
  fi

  if [[ -f "${1}/GDK_ROOT" && -x "${1}/gem/bin/gdk" ]]; then
    echo "${1}"
  else
    one_path_back="$(cd "${1}/.." || exit ; pwd)"
    find_gdk_root "${one_path_back}"
  fi
}

gdk_root_path=$(find_gdk_root "$(pwd)")
debug "gdk_root_path: $gdk_root_path"

if [[ -d "${gdk_root_path}" ]]; then
  "${gdk_root_path}/gem/bin/gdk" "${@}"
  gdk_exit_status=$?
  debug "ruby GDK exit status ${gdk_exit_status}"

  if [[ $gdk_exit_status == "$ERROR_CODE_ASDF_SHIM_BINARY_NOT_FOUND" ]]; then
    echo -e "\nERROR: failed to run 'gem/bin/gdk' Ruby script, try 'asdf' recovery procedure by running:" >&2
    echo "  (cd ${gdk_root_path} && asdf plugin-update ruby && asdf install ruby)"
  fi
else
  echo "ERROR: The current working directory is not a GDK." >&2
fi
