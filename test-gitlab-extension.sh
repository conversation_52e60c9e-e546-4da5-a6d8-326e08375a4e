#!/bin/bash

# GitLab VSCode Extension Testing and Monitoring Script
# This script helps you test your local GitLab VSCode extension with comprehensive logging

set -e

echo "🚀 GitLab VSCode Extension Testing Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if local GitLab is running
check_gitlab() {
    print_info "Checking if local GitLab is running..."
    if curl -s -I http://localhost:3000 > /dev/null 2>&1; then
        print_status "Local GitLab is running at http://localhost:3000"
    else
        print_error "Local GitLab is not running at http://localhost:3000"
        print_info "Please start your GitLab Development Kit (GDK)"
        exit 1
    fi
}

# Check if AI Gateway is running
check_ai_gateway() {
    print_info "Checking if AI Gateway is running..."
    if curl -s -I http://localhost:5052/monitoring/healthz > /dev/null 2>&1; then
        print_status "AI Gateway is running at http://localhost:5052"
    else
        print_warning "AI Gateway might not be running at http://localhost:5052"
        print_info "This is optional for basic extension testing"
    fi
}

# Check if Duo Workflow Service is running
check_duo_workflow() {
    print_info "Checking if Duo Workflow Service is running..."
    # Check if the service is listening on its typical port
    if lsof -i :50051 > /dev/null 2>&1; then
        print_status "Duo Workflow Service appears to be running"
    else
        print_warning "Duo Workflow Service might not be running"
        print_info "This is optional for basic extension testing"
    fi
}

# Start log monitoring
start_log_monitoring() {
    print_info "Starting log monitoring..."
    
    # Create log directory if it doesn't exist
    mkdir -p logs
    
    # Start monitoring AI Gateway logs if available
    if [ -f "gitlab-development-kit/gitlab-ai-gateway/ai-gateway.log" ]; then
        print_status "Monitoring AI Gateway logs..."
        tail -f gitlab-development-kit/gitlab-ai-gateway/ai-gateway.log > logs/ai-gateway-monitor.log 2>&1 &
        AI_GATEWAY_PID=$!
        echo $AI_GATEWAY_PID > logs/ai-gateway-monitor.pid
    fi
    
    # Start monitoring Duo Workflow Service logs if available
    if [ -f "gitlab-development-kit/gitlab-ai-gateway/********************/service.log" ]; then
        print_status "Monitoring Duo Workflow Service logs..."
        tail -f gitlab-development-kit/gitlab-ai-gateway/********************/service.log > logs/duo-workflow-monitor.log 2>&1 &
        DUO_WORKFLOW_PID=$!
        echo $DUO_WORKFLOW_PID > logs/duo-workflow-monitor.pid
    fi
    
    print_status "Log monitoring started. Check the 'logs/' directory for output."
}

# Stop log monitoring
stop_log_monitoring() {
    print_info "Stopping log monitoring..."
    
    if [ -f "logs/ai-gateway-monitor.pid" ]; then
        kill $(cat logs/ai-gateway-monitor.pid) 2>/dev/null || true
        rm logs/ai-gateway-monitor.pid
    fi
    
    if [ -f "logs/duo-workflow-monitor.pid" ]; then
        kill $(cat logs/duo-workflow-monitor.pid) 2>/dev/null || true
        rm logs/duo-workflow-monitor.pid
    fi
    
    print_status "Log monitoring stopped."
}

# Show extension logs
show_extension_logs() {
    print_info "Extension logs will be visible in VSCode's Output panel"
    print_info "To view them:"
    print_info "1. In Extension Development Host window: View → Output"
    print_info "2. Select 'GitLab Workflow' from the dropdown"
    print_info "3. Look for detailed fetch logs and debug information"
}

# Main execution
case "${1:-help}" in
    "start")
        check_gitlab
        check_ai_gateway
        check_duo_workflow
        start_log_monitoring
        show_extension_logs
        print_status "Monitoring setup complete!"
        print_info "Now launch your extension with F5 in VSCode"
        print_info "Use 'Debug Extension with Duo Platform' configuration for enhanced logging"
        ;;
    "stop")
        stop_log_monitoring
        ;;
    "status")
        check_gitlab
        check_ai_gateway
        check_duo_workflow
        ;;
    "logs")
        print_info "Available log files:"
        find logs/ -name "*.log" 2>/dev/null | while read log; do
            echo "  - $log"
        done
        ;;
    "help"|*)
        echo "Usage: $0 {start|stop|status|logs|help}"
        echo ""
        echo "Commands:"
        echo "  start   - Start monitoring and check services"
        echo "  stop    - Stop log monitoring"
        echo "  status  - Check status of all services"
        echo "  logs    - List available log files"
        echo "  help    - Show this help message"
        ;;
esac
