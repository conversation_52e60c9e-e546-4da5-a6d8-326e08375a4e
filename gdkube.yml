apiVersion: v1
kind: Service
metadata:
  name: gdk
  labels:
    app: gdk
spec:
  ports:
    - port: 3000
  selector:
    app: gdk
  type: NodePort
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gdk
spec:
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: gdk
  template:
    metadata:
      labels:
        app: gdk
    spec:
      containers:
        - name: gdk
          image: registry.gitlab.com/gitlab-org/gitlab-development-kit
          imagePullPolicy: Always
          ports:
          - name: gdk
            containerPort: 3000
          command:
            - /bin/bash
            - -c
            - >
              source /home/<USER>/.bash_profile;
              gem install gitlab-development-kit;
              cd /home/<USER>
              git clone https://gitlab.com/gitlab-org/gitlab-development-kit.git;
              cd gitlab-development-kit;
              echo 'hostname: 0.0.0.0' > gdk.yml;
              gdk install;
              gdk start;
              gdk tail
