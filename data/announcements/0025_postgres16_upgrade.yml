header: PostgreSQL 16 is now default in GDK
body: |
  We've updated GDK to use PostgreSQL 16 as the default database version.

  During `gdk update`, `pg_upgrade` has been run to migrate your data from PostgreSQL 14 to 16. A backup of your PostgreSQL 14.9 data directory will be preserved in the `postgres/` directory.

  If you encounter any issues on macOS, please ensure you are running the latest version of macOS before seeking support.

  No manual configuration changes are required for this migration.

  See https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2302 for more details.
