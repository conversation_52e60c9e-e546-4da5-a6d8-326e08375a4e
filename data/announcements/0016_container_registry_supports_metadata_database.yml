---
header: Container registry now supports running with the metadata database.
body: |
  The Container metadata database enables many new registry features, including online garbage collection, and increases the efficiency of many registry operations.

  By default, the GDK Container registry still runs without the metadata database. To opt-in to using the metadata database in your GDK environment follow the steps detailed in: https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/doc/howto/registry.md?ref_type=heads#metadata-database.

  For more details on the Container registry metadata database, see https://docs.gitlab.com/ee/administration/packages/container_registry_metadata_database.html.
