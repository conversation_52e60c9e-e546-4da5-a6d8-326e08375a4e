---
header: GitLab Topology Service is now enabled by default
body: |
  In order to prepare for Cells Phase 3 rollout, Topology Service is now enabled by default.
  If you face any problems, you can disable it using

  ```
  gdk config set gitlab_topology_service.enabled false
  gdk reconfigure
  ```

  For those who had enable it before, you might get errors in the browser due to this
  issue: https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2266
  Deleting the cookies for http://gdk.test in your browser should resolve the problem.
