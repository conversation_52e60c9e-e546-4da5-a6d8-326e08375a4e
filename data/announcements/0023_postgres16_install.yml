header: GDK now installs PostgreSQL 16
body: |
  This does not change the default PostgreSQL version but makes version 16 available for use.

  PostgreSQL 13 is no longer supported by GDK as it is not compatible with GitLab 17 onward.

  No configuration changes are required.

  This prepares GDK for a future migration to PostgreSQL 16, which will be announced separately when ready.

  See https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2302 for more details.
