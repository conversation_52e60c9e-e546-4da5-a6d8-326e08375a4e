---
header: GDK now uses HTTP router by default
body: |
  HTTP router is now available by default. To disable it, simply add the following lines to your gdk.yml file:
  ```
  gitlab_http_router:
    enabled: false
  ```

  or run `gdk config set gitlab_http_router.enabled false`

  After adding these lines, please run `gdk reconfigure` to apply the changes.

  For more details, see
  https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2102.
