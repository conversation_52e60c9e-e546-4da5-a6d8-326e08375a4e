---
header: Topology Service Client Configuration Path Changed
body: |
  The configuration path for the topology service client has been relocated to better reflect its relationship to cells.

  Previous configuration in gdk.yml:

  ```yaml
  cells:
    instances:
    - config:
        gitlab:
          topology_service:
            enabled: true
  gitlab:
    topology_service:
      enabled: true
  ```
  New configuration in gdk.yml:

  ```yaml
  cells:
    instances:
    - config:
        gitlab:
          cell:
            topology_service_client:
              enabled: true
  gitlab:
    cell:
      topology_service_client:
        enabled: true
  ```
  This change nests the topology_service_client under the cell namespace to more accurately represent its scope and function.
