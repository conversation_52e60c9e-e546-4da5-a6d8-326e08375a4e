---
header: GDK installation now supports mise as active version manager
body: |
  You can now select [mise](https://mise.jdx.dev/) as the default tool version manager during GDK installation. This means you no longer need to switch to mise from asdf after installation.
  
  To install GDK with mise, please run:

  ```
  curl "https://gitlab.com/gitlab-org/gitlab-development-kit/-/raw/main/support/install" | bash
  ```

  For more details, see <https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2346>.
