---
header: GDK now uses precompiled Ruby by default
body: |
  GDK now downloads and uses precompiled Ruby binaries by default.
  This change speeds up Ruby installation by removing the need to compile from source.

  If you prefer to build Ruby from source, you can disable precompiled binaries with:

  ```
  gdk config set gdk.use_precompiled_ruby false
  ```

  Then run `gdk update` to apply the new setting.

  For more details, see <https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2932>.
