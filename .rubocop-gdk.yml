---
inherit_gem:
  gitlab-styles:
    - rubocop-default.yml

require:
  - ./rubocop/rubocop
  - rubocop-rake

AllCops:
  NewCops: enable
  TargetRubyVersion: 3.2
  Exclude:
    - 'GDK_ROOT'
    - '.backups/**/*'
    - '.cache/**/*'
    - '.git/**/*'
    - 'Brewfile'
    - 'Dangerfile'
    - 'charts-gitlab/**/*'
    - 'danger/**/*'
    - 'dev/**/*'
    - 'duo-workflow-executor/**/*'
    - 'duo-workflow-service/**/*'
    - 'elasticsearch/**/*'
    - 'gitaly/**/*'
    - 'gitlab-ai-gateway/**/*'
    - 'gitlab-cells/**/*'
    - 'gitlab-docs/**/*'
    - 'gitlab-elasticsearch-indexer/**/*'
    - 'gitlab-eli5/**/*'
    - 'gitlab-http-router/**/*'
    - 'gitlab-k8s-agent/**/*'
    - 'gitlab-observability-backend/**/*'
    - 'gitlab-operator/**/*'
    - 'gitlab-pages/**/*'
    - 'gitlab-runner/**/*'
    - 'gitlab-shell/**/*'
    - 'gitlab-spamcheck/**/*'
    - 'gitlab-topology-service/**/*'
    - 'gitlab-ui/**/*'
    - 'gitlab-workhorse/**/*'
    - 'gitlab/**/*'
    - 'go-gitlab-shell/**/*'
    - 'grafana/**/*'
    - 'influxdb/**/*'
    - 'jaeger/**/*'
    - 'log/**/*'
    - 'minio/**/*'
    - 'node_modules/**/*'
    - 'omnibus-gitlab/**/*'
    - 'postgresql-geo/**/*'
    - 'postgresql-primary/**/*'
    - 'postgresql/**/*'
    - 'repositories/**/*'
    - 'services/**/*'
    - 'sv/**/*'
    - 'tmp/**/*'
    - 'vendor/**/*'
    - 'zoekt/**/*'

CodeReuse/ActiveRecord:
  Enabled: false

Layout/LineLength:
  Enabled: false

Rails:
  Enabled: false

RSpec/ContextWording:
  Enabled: false

RSpec/MultipleMemoizedHelpers:
  Enabled: false

RSpec/PredicateMatcher:
  Enabled: false

RSpec/NamedSubject:
  Enabled: false

Style/ArgumentsForwarding:
  Enabled: false

Naming/BlockForwarding:
  Enabled: false
