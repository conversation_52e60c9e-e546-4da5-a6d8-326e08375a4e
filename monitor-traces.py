#!/usr/bin/env python3
import time
import json
import sys
from pathlib import Path

def monitor_traces():
    log_files = [
        "log/duo-workflow-service/current",
        "log/gitlab-ai-gateway/gateway_debug.log"
    ]
    
    print("🔍 Monitoring traces in real-time...")
    print("Press Ctrl+C to stop")
    
    # Keep track of last position in each file
    positions = {}
    
    try:
        while True:
            for log_file in log_files:
                log_path = Path(log_file)
                if not log_path.exists():
                    continue
                    
                # Get current position
                current_pos = positions.get(log_file, 0)
                
                with open(log_path, 'r') as f:
                    f.seek(current_pos)
                    new_lines = f.readlines()
                    positions[log_file] = f.tell()
                
                # Filter for trace-related lines
                for line in new_lines:
                    if any(keyword in line.lower() for keyword in [
                        'trace', 'langsmith', 'run_tree', 'workflow', 
                        'duo', 'agent', 'tool', 'llm', 'rathid-gdk'
                    ]):
                        timestamp = time.strftime("%H:%M:%S")
                        print(f"[{timestamp}] {Path(log_file).name}: {line.strip()}")
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 Stopped monitoring")

if __name__ == "__main__":
    monitor_traces()
