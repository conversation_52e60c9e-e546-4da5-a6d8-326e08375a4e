{"id": 253185774, "head_commit_sha": "1a5dcb8344efb09b442035e1de3a1603da959cda", "base_commit_sha": "dbf0405a4ea2c26a76cce67aeb7862f78a8d78c8", "start_commit_sha": "dbf0405a4ea2c26a76cce67aeb7862f78a8d78c8", "created_at": "2021-09-29T13:25:36.333Z", "merge_request_id": 118935419, "state": "collected", "real_size": "1", "commits": [{"id": "1a5dcb8344efb09b442035e1de3a1603da959cda", "short_id": "1a5dcb83", "created_at": "2021-09-29T13:20:33.000Z", "parent_ids": [], "title": "test: add more folder", "message": "test: add more folder\n", "author_name": "<PERSON><PERSON>", "author_email": "<EMAIL>", "authored_date": "2021-09-29T13:20:33.000Z", "committer_name": "<PERSON><PERSON>", "committer_email": "<EMAIL>", "committed_date": "2021-09-29T13:20:33.000Z", "trailers": {}, "web_url": "https://gitlab.com/jinliming2/test/-/commit/1a5dcb8344efb09b442035e1de3a1603da959cda"}], "diffs": [{"old_path": "folder1/folder2/folder3/folder4/folder5/test.txt", "new_path": "folder1/folder2/folder3/folder4/folder5/test.txt", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "@@ -0,0 +1 @@\n+Hello World!\n"}]}