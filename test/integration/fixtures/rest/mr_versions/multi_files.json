{"id": 253186195, "head_commit_sha": "e919975a517cb2b17edf22cdfbcee19e6c523d26", "base_commit_sha": "dbf0405a4ea2c26a76cce67aeb7862f78a8d78c8", "start_commit_sha": "dbf0405a4ea2c26a76cce67aeb7862f78a8d78c8", "created_at": "2021-09-29T13:26:03.737Z", "merge_request_id": 118935505, "state": "collected", "real_size": "4", "commits": [{"id": "e919975a517cb2b17edf22cdfbcee19e6c523d26", "short_id": "e919975a", "created_at": "2021-09-29T13:24:21.000Z", "parent_ids": [], "title": "test: add file tree", "message": "test: add file tree\n", "author_name": "<PERSON><PERSON>", "author_email": "<EMAIL>", "authored_date": "2021-09-29T13:24:21.000Z", "committer_name": "<PERSON><PERSON>", "committer_email": "<EMAIL>", "committed_date": "2021-09-29T13:24:21.000Z", "trailers": {}, "web_url": "https://gitlab.com/jinliming2/test/-/commit/e919975a517cb2b17edf22cdfbcee19e6c523d26"}], "diffs": [{"old_path": "folder1/file1", "new_path": "folder1/file1", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "@@ -0,0 +1 @@\n+folder1/file1\n"}, {"old_path": "folder2/folder3/folder4/folder5/file3", "new_path": "folder2/folder3/folder4/folder5/file3", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "@@ -0,0 +1 @@\n+folder2/folder3/folder4/folder5/file3\n"}, {"old_path": "folder2/folder3/file2", "new_path": "folder2/folder3/file2", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "@@ -0,0 +1 @@\n+folder2/folder3/file2\n"}, {"old_path": "file1", "new_path": "file1", "a_mode": "0", "b_mode": "100644", "new_file": true, "renamed_file": false, "deleted_file": false, "diff": "@@ -0,0 +1 @@\n+file1\n"}]}