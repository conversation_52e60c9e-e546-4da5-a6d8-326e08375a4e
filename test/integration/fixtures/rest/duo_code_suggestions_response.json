{"id": "cmpl-8yKS7uITRGpLVYb07UiNF7afqAJzb", "model": "codegen", "object": "text_completion", "created": 1665649251, "choices": [{"text": "\nimport numpy as np\nimport matplotlib.pyplot as pl", "index": 0, "finish_reason": "length", "logprobs": {"token_logprobs": [-0.07883310317993164, -0.02885151468217373, -0.07230474054813385, -0.00017865108384285122, -0.00010133303294423968, -3.4570753086882178e-06, -0.000912367133423686, -0.15801125764846802, -0.7833877801895142, -9.536747711536009e-07, -9.536747711536009e-07, -0.0036512434016913176, -0.0002885877329390496, -9.536747711536009e-07, -5.1260126383567695e-06, -2.837221290974412e-05], "top_logprobs": [{"\n": -0.07883310317993164}, {"import": -0.02885151468217373}, {" n": -0.07230474054813385}, {"umpy": -0.00017865108384285122}, {" as": -0.00010133303294423968}, {" np": -3.4570753086882178e-06}, {"\n": -0.000912367133423686}, {"import": -0.15801125764846802}, {" mat": -0.7833877801895142}, {"plot": -9.536747711536009e-07}, {"lib": -9.536747711536009e-07}, {".": -0.0036512434016913176}, {"py": -0.0002885877329390496}, {"plot": -9.536747711536009e-07}, {" as": -5.1260126383567695e-06}, {" pl": -2.837221290974412e-05}], "tokens": ["\n", "import", " n", "umpy", " as", " np", "\n", "import", " mat", "plot", "lib", ".", "py", "plot", " as", " pl"], "text_offset": [19, 20, 26, 28, 32, 35, 38, 39, 45, 49, 53, 56, 57, 59, 63, 66]}}], "usage": {"completion_tokens": 16, "prompt_tokens": 6, "total_tokens": 22}}