FROM ubuntu:22.04
LABEL authors.maintainer="GDK contributors: https://gitlab.com/gitlab-org/gitlab-development-kit/-/graphs/main"

## The CI script that build this file can be found under: support/docker

ENV DEBIAN_FRONTEND=noninteractive
ENV LC_ALL=en_US.UTF-8
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US.UTF-8
ENV GDK_DEBUG=true

# See https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2807
ARG mise_http_timeout=60s
ENV MISE_HTTP_TIMEOUT $mise_http_timeout
ARG mise_fetch_remote_versions_timeout=60s
ENV MISE_FETCH_REMOTE_VERSIONS_TIMEOUT $mise_fetch_remote_versions_timeout

RUN apt-get update && \
    apt-get install -y \
      curl \
      libssl-dev \
      locales \
      locales-all \
      pkg-config \
      software-properties-common \
      sudo && \
    add-apt-repository ppa:git-core/ppa -y

RUN useradd --user-group --create-home --groups sudo gdk && \
    echo "gdk ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/gdk_no_password

WORKDIR /home/<USER>/tmp
RUN chown -R gdk:gdk /home/<USER>

USER gdk
COPY --chown=gdk . .

ENV PATH="/home/<USER>/.local/bin:/home/<USER>/.local/share/mise/bin:/home/<USER>/.local/share/mise/shims:${PATH}"

RUN echo "tool_version_manager:" > gdk.yml && \
    echo "  enabled: true" >> gdk.yml && \
    bash ./support/bootstrap

# Verify tools and cleanup
RUN bash -eclx "mise version; yarn --version; node --version; ruby --version" && \
    sudo apt-get purge software-properties-common -y && \
    sudo apt-get clean -y && \
    sudo apt-get autoremove -y && \
    sudo rm -rf \
      "$HOME/.cache/" \
      "$HOME/tmp" \
      /tmp/* \
      /var/cache/apt/* \
      /var/lib/apt/lists/* \
      $(ls -d "$HOME/gdk/gitaly/_build/"* | grep -v /bin)

WORKDIR /home/<USER>
