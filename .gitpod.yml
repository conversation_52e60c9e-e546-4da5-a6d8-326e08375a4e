# Please adjust to your needs (see https://www.gitpod.io/docs/introduction/learn-gitpod/gitpod-yaml)

tasks:
  # init is executed when workspace is created
  - init: |
      echo "--- adding community fork for GDK into remotes ---"
      git remote add patches https://gitlab.com/gitlab-community/gitlab-org/gitlab-development-kit.git

      echo "--- install rspec, lefthook and other dev gems ---"
      bundle install
      lefthook install

      # install dev dependencies
      brew install shellcheck
      brew install vale
