---
title: Troubleshooting mise
---

The following are possible solutions to problems you might encounter with
[mise](https://mise.jdx.dev/) and GDK.

If your issue is not listed here:

- For generic mise problems, raise an issue or pull request in the [mise project](https://github.com/jdx/mise).
- For GDK-specific issues, raise an issue or merge request in the [GDK project](https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues).

If you are a GitLab team member, you can also ask for help with troubleshooting in
the `#mise` Slack channel. If your problem is GDK-specific, use the
`#gdk` channel so more people can see it.

## Error: `No such file or directory` when installing

You might have `mise install` fail with a cache error like the following.

```shell
$ mise install
mise ruby build tool update error: failed to update ruby-build: No such file or directory (os error 2)
mise failed to execute command: ~/Library/Caches/mise/ruby/ruby-build/bin/ruby-build 3.2.5 /Users/<USER>/.local/share/mise/installs/ruby/3.2.5
mise No such file or directory (os error 2)
```

You can usually fix this by cleaning the mise cache: `mise cache clear`

## Error `~/.local/share/mise/plugins/yarn/bin/list-all: timed out: timed out waiting on channel`

If you use SSH to connect to GitHub, `yarn` might fail because of a timeout. You can temporarily use HTTPS by commenting out the following lines in `~/.gitconfig`:

```shell
#[url "**************:"]
#    insteadof = https://github.com/
```

## Error: `command not found: gdk` or `mise is not activated`

Check steps in <https://mise.jdx.dev/getting-started.html#activate-mise> to ensure you have activated `mise` correctly.

If you use `zsh` than this should do the trick:

```shell
echo 'eval "$(mise activate zsh)"' >> ~/.zshrc
source ~/.zshrc
```

If you are seeing warnings like `mise WARN missing: ruby@3.3.9`, run `mise install` to install the missing Ruby versions.

If after that, the `gdk` command is still missing, run `gem install gitlab-development-kit` and optionally `mise reshim`
to install the [GDK gem](https://rubygems.org/gems/gitlab-development-kit) that includes the command.

## Mise not reading default configuration file

In some cases, you may get into a situation where `mise` doesn't read/parse your existing config. In this state, `mise config` will return an empty list, despite your config being stored in a known config path (e.g. `~/.config/mise/config.toml`)

```shell
gdk@c2c644400e13:/gitlab-gdk$ mise config
Path  Tools
gdk@c2c644400e13:/gitlab-gdk$ ls ~/.config/mise/config.toml
/home/<USER>/.config/mise/config.toml
```

This could be due to a previous command setting that config file to untrusted. To reverse this, you can execute `mise trust <file>` and it should resolve the issue.

```shell
gdk@c2c644400e13:/gitlab-gdk$ mise config
Path  Tools
gdk@c2c644400e13:/gitlab-gdk$ mise trust ~/.config/mise/config.toml
mise trusted /home/<USER>
gdk@c2c644400e13:/gitlab-gdk$ mise config
Path                        Tools
~/.config/mise/config.toml  (none)
```

In some limited circumstances, this approach may not work. In that case, perhaps try following the troubleshooting in [this article](https://glenn-roberts.com/posts/2025/03/11/when-mise-ignores-your-global-config-a-tale-of-red-herrings-and-stale-state/).
