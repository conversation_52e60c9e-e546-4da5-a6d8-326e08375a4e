---
title: Support process
---

As GDK engineers on a small team, we are at the forefront of support.
Especially with team members through the `#gdk` Slack channel, we have a
direct line to our largest user demographic.

This extreme proximity to our users is huge opportunity but can also be a
major challenge to our planned work.

## Team member support

When a team member seeks support in the `#gdk` Slack channel and their
problem doesn't have an obvious, known solution, ask them to perform the
following steps.

> [!note]
> You can also react with the `:mag:` (🔍) emoji on their message at any
> time and an automation asks them to follow these steps.

1. Ask if the user ran `gdk doctor`, which can already give instructions for known problems
1. If that didn't resolve it, ask them to run `gdk report` as per [the troubleshooting guide](../troubleshooting/_index.md#get-help-when-troubleshooting) and to reply with the report ID or issue link
1. Investigate the report provided by the user

### Synchronous support

If our capacity permits or a problem has a high severity or impact, we
may offer synchronous support in a Zoom call. Either use the `/zoom`
command in the `#gdk` channel, so everyone can contribute, or schedule a
call directly with the affected user.

### Alternate installations

In case a GDK installation becomes unusable despite re-installation of
GDK or re-installation is unfavorable, suggest running GDK in other
environments.

- [GDK-in-a-box](../gdk_in_a_box.md)
- [GDK-in-a-box container](https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/support/gdk-in-a-box/container/README.md)

## Community and less urgent support

Some issues aren't immediately blocking to users but may still require a
technical remediation in GDK.

Use the ~"development-tooling::support request" label to mark these issues,
so we can direct our efforts and highlight them in our work reviews.
