---
title: GDK telemetry
---

> [!note]
> This page is a technical document, not a legal one.

You can opt in to GDK collecting telemetry data about your installation,
how GDK performs, and how stable it is. We use this data to make
informed, data-driven decisions about how to improve GDK.

Telemetry is pseudonymized using a random telemetry ID that is uniquely
generated per GDK installation.

As of March 28th, GitLab team members are automatically enrolled in telemetry
[as per this issue](https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2529).

Most of the telemetry-related code can be found in `lib/gdk/telemetry.rb`.

## What GDK collects

In broad terms, GDK collects the following information:

- Device metadata (e.g., processor architecture and core count)
- Installed software (e.g., operating system, `mise`)
- Command usage (`gdk` commands you ran, including duration and stability)
- Configured GDK services
- Exception backtraces and GDK-related logs
- Whether or not you are a GitLab team member

## How we use telemetry to improve GDK

In the past, we have already used telemetry to validate that we actually
[sped up GDK updates through parallelization](https://codingpa.ws/post/faster-updates-with-fancy-spinners).

In January of 2025, we created the first iteration of the
[GDK stats dashboard](https://gdk-stats-826305.gitlab.io/), which we
frequently refer to when making changes to GDK.

## How to access telemetry data

Team members who need access to telemetry data to work on GDK can submit
an [access request](https://gitlab.com/gitlab-com/team-member-epics/access-requests)
for `ClickHouse Cloud`.

After you have access, open the instance `product-analytics-prd` in
ClickHouse and use the `[EP] ClickHouse Cloud Login` password from the
`Development Tooling` 1Password vault.
