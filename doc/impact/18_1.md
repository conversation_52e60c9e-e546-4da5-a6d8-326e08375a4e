---
title: 18.1 milestone
---

{{< callout emoji="🚀" type="info" >}}
  Because GDK is updated continuously, there is no specific release.
  The changes listed below were shipped during the milestone.<br/>
  Don't see them? Try running `gdk update`.
{{< /callout >}}

During the 18.1 milestone, we shipped over 110 improvements to GDK and
the documentation, including 24 bug fixes.

The following are a few notable changes we'd like to highlight!

## New documentation site

The GDK documentation now comes in a more navigable format with improved
search: a new, standalone documentation website!

![A screenshot of the new GDK documentation site's landing page.](img/18_1/docs.png)

Check out <https://gitlab-org.gitlab.io/gitlab-development-kit/> and add it to your bookmarks.

## Improved support experience

In this quarter, we are putting a special focus on improving the GDK
troubleshooting and support experience.

To avoid having to repeatedly ask you multiple questions about your
setup, we have introduced a new `gdk report` command. It collects
relevant information about your GDK installation and allows you to create
a report issue.

- [How to use `gdk report`](../troubleshooting/_index.md#get-help-when-troubleshooting)
- [Feedback issue](https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2671)
- [Related epic](https://gitlab.com/groups/gitlab-org/quality/tooling/-/epics/6)

## Precompiled binaries in RSpec

Over the past few quarters, we introduced precompiled binaries for
services like Gitaly and Workhorse. This speeds up GDK updates because
your machine no longer has to compile the binaries.

Now, we're extending this to RSpec in GitLab monolith. Once GDK has
downloaded the binaries, you should see up to an 80% reduction in RSpec
setup time.

We're also tracking RSpec setup performance through GDK telemetry if
you've [configured telemetry](../contributing/telemetry.md).

> [!TIP]
> Use `gdk switch [branch]` to get the correct binary versions for your
> current branch.

- [Related epic](https://gitlab.com/groups/gitlab-org/-/epics/16887)

## Switching to branches with GDK

Did you ever want to set GDK and its services to a branch you are
working on or reviewing?

Run `gdk switch [branch]` to switch to a branch in the GitLab repository
and set all services to the relevant versions.

- [Feedback issue](https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2649)

## Update stability improvements

In the current quarter, we are also investing in the stability of GDK
updates (performed with `gdk update`). Our goal is to achieve an update
success rate of 90% by the end of the quarter.

We are actively working on multiple small improvements, including sending
redacted crash reports to our Sentry instance to discover and fix bugs in
GDK.

- [Related epic](https://gitlab.com/groups/gitlab-org/quality/tooling/-/epics/44)

## New `gdk rake` command

Following multiple problems and incompatibilities when using
`bundle exec` with Rake tasks in GDK, we have added a `gdk rake` command.

To run a GDK-specific Rake task, use `gdk rake` over `bundle exec rake`.

- [Issue](https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2690)

## Removed GitLab UI

We have removed the GitLab UI service from GDK because it was integrated
in the [`design.gitlab.com` project](https://gitlab.com/gitlab-org/gitlab-services/design.gitlab.com/-/issues/2034#post-merge-things-to-address). The project's `Yalc` tooling allows straightforward setup in `package.json`.

- [Issue](https://gitlab.com/gitlab-org/gitlab-development-kit/-/issues/2728)

## Community contributions

To the wider GitLab community, thank you for your [six contributions to GDK](https://gitlab.com/gitlab-org/gitlab-development-kit/-/merge_requests/?sort=merged_at_desc&state=merged&label_name%5B%5D=Community%20contribution&milestone_title=18.1&first_page_size=20).

At GitLab, [everyone can contribute](https://about.gitlab.com/community/contribute/) and we couldn't have done it without you!
