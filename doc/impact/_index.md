---
title: Impact notes
---

Starting with the 18.1 milestone, we have started publishing impact notes
for GDK in sync with GitLab releases.

The goal of these impact notes is to highlight the tangible impact of
any improvements and changes we've shipped during the milestone for GDK
users.

## Links

- [18.1 milestone](18_1.md)

## Process

At the end of every GitLab milestone, the Development Tooling team:

1. Collects all major changes that we've shipped during the milestone.
1. A few days after the release notes for the GitLab release have been published, merges the GDK impact notes which are then published to the GitLab Pages site.
1. Posts a link to the impact notes in the `#gdk` Slack channel.
