---
name: gitlab_docs.Badges-Offerings
description: |
  Tests the offering information in the tier badges that appear below topic titles.
  For a list of all options, see https://docs.gitlab.com/development/documentation/styleguide/availability_details/#available-options
extends: existence
message: "Offerings should be comma-separated and capitalized, without `and` or bold/italics. Example: `- Offering: GitLab.com, GitLab Self-Managed, GitLab Dedicated`."
link: https://docs.gitlab.com/development/documentation/styleguide/availability_details/#available-options
vocab: false
level: error
nonword: true
scope: raw
tokens:
  - ^- Offering:[^\n]*(SaaS|[Ss]elf-managed|dedicated|and|Dedicated,|, GitLab\.com)
  - ^- Offering:[^\n]*(?<!GitLab )(Self-Managed|Dedicated)
  - ^(- )?\*+Offering(:\*+|\*+:)
