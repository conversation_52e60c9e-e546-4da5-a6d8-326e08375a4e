---
name: gitlab_docs.AlertBoxStyle
description: |
  Makes sure alert boxes use Hugo shortcodes.
extends: existence
message: "Alert boxes are defined with hugo shortcodes. View the style guide for details."
link: https://docs.gitlab.com/development/documentation/styleguide/#alert-boxes
vocab: false
ignorecase: true
level: error
nonword: true
scope: raw
tokens:
  - '^ *(> )?(- )?\**(note|tip|caution|danger|warning|flag|disclaimer|details|history)\**:\**$'
