---
name: gitlab_docs.Badges-Tiers
description: |
  Tests the tier information in the tier badges that appear below topic titles.
  For a list of all options, see https://docs.gitlab.com/development/documentation/styleguide/availability_details/#available-options
extends: existence
message: "Tiers should be capitalized, comma-separated, without bold/italics, and ordered lowest to highest. Example: `- Tier: Free, Premium, Ultimate`."
link: https://docs.gitlab.com/development/documentation/styleguide/availability_details/#available-options
vocab: false
level: error
nonword: true
scope: raw
tokens:
- ^- Tier:.*(free(?!-)|premium|ultimate|, Free|Ultimate,)
- ^(- )?\*+Tier(:\*+|\*+:)
