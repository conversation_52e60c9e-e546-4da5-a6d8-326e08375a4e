---
name: gitlab_docs.FrontMatter
extends: script
description: |
  Ensures all pages have frontmatter and set a page title.
message: "Page must have valid frontmatter that includes a 'title' field."
link: https://docs.gitlab.com/development/documentation/metadata/
level: error
scope: raw
script: |
  text := import("text")
  matches := []

  // Initialize variables
  frontmatterDelimiterCount := 0
  frontmatter := ""
  hasError := false

  for line in text.split(scope, "\n") {
    // Check if frontmatter exists
    if !text.re_match("^---\n", scope) {
      start := text.index(scope, line)
      matches = append(matches, {begin: start, end: start + len(line)})
      hasError = true
      break
    }

    if frontmatterDelimiterCount == 1 {
      frontmatter += line + "\n"
    }
    if frontmatterDelimiterCount == 2 {
      break
    }
    if text.re_match("^---", line) {
      frontmatterDelimiterCount++
      start := text.index(scope, line)
      matches = append(matches, {begin: start, end: start + len(line)})
    }
  }

  // Check for unclosed frontmatter
  if frontmatterDelimiterCount != 2 {
    hasError = true
  }

  // Check if the page has redirect_to (these pages don't need titles)
  hasRedirectTo := text.re_match("(?m)^redirect_to:", frontmatter)
  if !hasRedirectTo {
    // First check if we have a title key at all
    hasTitleKey := text.re_match("(?m)^[tT]itle:", frontmatter)
    // Then check if it has content (anything but whitespace) after the colon
    hasValidTitle := text.re_match("(?m)^[tT]itle:[^\\n]*[^\\s][^\\n]*$", frontmatter)
    if !hasError && (!hasTitleKey || !hasValidTitle) {
      hasError = true
    }
  }

  if !hasError {
    matches = []
  }
