---
name: gitlab_base.OxfordComma
description: |
  Checks for the lack of an Oxford comma. In some cases, will catch overly
  complex sentence structures with lots of commas.
extends: existence
message: "Use a comma before the last 'and' or 'or' in a list of four or more items."
link: https://docs.gitlab.com/development/documentation/styleguide/#punctuation
vocab: false
level: warning
raw:
  - '(?:[\w-_` ]+,){2,}(?:[\w-_` ]+) (and |or )'
