---
name: gitlab_base.UnclearAntecedent
description: |
  Checks for words that need a noun for clarity.
extends: existence
message: "Instead of '%s', try starting this sentence with a specific subject and verb."
link: https://docs.gitlab.com/development/documentation/styleguide/word_list/#this-these-that-those
vocab: false
level: warning
ignorecase: false
tokens:
  - 'That is'
  - 'That was'
  - 'There are'
  - 'There were'
  - 'These are'
  - 'These were'
  - 'This is'
  - 'This was'
  - 'Those are'
  - 'Those were'
