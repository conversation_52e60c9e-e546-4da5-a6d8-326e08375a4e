---
name: gitlab_base.SentenceSpacing
description: |
  Checks for incorrect spacing (no spaces, or more than one space) around punctuation.
extends: existence
message: "Use exactly one space with punctuation. Check '%s' for spacing problems."
link: https://docs.gitlab.com/development/documentation/styleguide/#punctuation
vocab: false
level: error
nonword: true
tokens:
  - '[a-z][.?!,][A-Z]'
  - '[\w.?!,\(\)\-":] {2,}[\w.?!,\(\)\-":]'
  - '[a-z] +[.?!,:] +'
