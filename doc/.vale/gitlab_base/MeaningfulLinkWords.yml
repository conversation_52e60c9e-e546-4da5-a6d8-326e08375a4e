---
name: gitlab_base.MeaningfulLinkWords
description: |
  Checks for the presence of semantically unhelpful words in link text.
extends: existence
message: "Improve SEO and accessibility by rewriting the link text for '%s'."
level: warning
ignorecase: true
link: https://docs.gitlab.com/development/documentation/styleguide/#text-for-links
vocab: false
scope: raw
nonword: true
tokens:
  - '\[here\](?=\(.*\))'
  - '\[this\](?=\(.*\))'
  - '\[this page\](?=\(.*\))'
