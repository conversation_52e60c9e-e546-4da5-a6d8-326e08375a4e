---
name: gitlab_base.Level
description: |
  Avoid variations on the phrase "instance level" and "group level"
extends: existence
message: "Avoid using 'level' when referring to groups, instances, or projects: '%s'"
link: https://docs.gitlab.com/development/documentation/styleguide/word_list/#level
vocab: false
level: suggestion
ignorecase: true
tokens:
  - 'instance level'
  - 'instance-level'
  - 'group level'
  - 'group-level'
  - 'project level'
  - 'project-level'
