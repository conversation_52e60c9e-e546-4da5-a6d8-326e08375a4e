---
title: GitLab Workhorse and GDK
---

Use GDK to develop changes to [GitLab Workhorse](https://docs.gitlab.com/development/workhorse/). GitLab Workhorse is a default component in GDK so it is always present.

## Recompile GitLab Workhorse with changes

GDK runs the binary at `<gdk-dir>/gitlab/gitlab-workhorse`. When you make code changes, you must recompile that binary and restart GitLab Workhorse:

```shell
cd <gdk-dir>/gitlab/workhorse
make
gdk restart workhorse
```

## Enable Prometheus metrics for GitLab Workhorse

GitLab Workhorse can provide Prometheus metrics but this is not enabled by default. To enable Prometheus metrics for GitLab Workhorse, run:

```shell
gdk config set prometheus.enabled true
gdk config set prometheus.workhorse_exporter_port 9230
gdk reconfigure
```

When enabled, GitLab Workhorse Prometheus metrics are available at: `http://<gdk_host>:9230/metrics`.
