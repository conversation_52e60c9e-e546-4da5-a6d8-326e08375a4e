---
title: <PERSON><PERSON>ao
---

OpenBao is backward compatible with <PERSON><PERSON> and can replace <PERSON><PERSON> without changing the existing setup. To avoid conflicts, disable <PERSON><PERSON> when enabling OpenBao.

## Important: Set up binary location

GDK builds OpenBao from [an internal build system](https://gitlab.com/gitlab-org/govern/secrets-management/openbao-internal), which includes custom patches. The binary is located at `openbao/bin/bao` in your GDK directory.

To use the OpenBao CLI commands:

1. Add the `BAO_ADDR` variable and binary to your PATH:

   ```shell
   # Add this to your .bashrc, .zshrc, or equivalent. Then run `source ~/.bashrc`.
   export BAO_ADDR='http://gdk.test:8200'
   export PATH="/path/to/your/gdk/openbao/bin:$PATH"
   ```

## Configure OpenBao for GDK

To configure [OpenBao](https://openbao.org) to run locally in GDK:

1. Enable openbao.

   ```shell
   gdk config set openbao.enabled true && gdk reconfigure
   ```

1. Create a configuration file.

   ```shell
   rake openbao/config.hcl
   ```

1. Run openbao and unseal the vault.

   ```shell
   gdk start openbao && gdk bao configure
   ```

   This command provides the following information:

   ```shell
   => "✅ OpenBao has been unsealed successfully"
   => "The root token is: s.xxxxxxxxxxxxxxx"
   ```
   
   Save the root token, which you need for login.

1. Run `bao login` with the root token from above.

   ```shell
   bao login <root_token>
   ```

   You can also fetch the root token with the following command: `gdk config get openbao.root_token`.

1. Enable JWT authentication.

   ```shell
   bao auth enable -path=gitlab_rails_jwt jwt
   ```

1. Configure the JWT authentication with my GDK GitLab OIDC discovery URL and the expected issuer:

   ```shell
   bao write auth/gitlab_rails_jwt/config \
      oidc_discovery_url="http://gdk.test:3000" \
      bound_issuer="http://gdk.test:3000"
   ```

1. Create a policy that grants the necessary permissions.

   ```shell
   bao policy write secrets_manager - <<EOF
   path "*" {
   capabilities = ["create", "read", "update", "delete", "list", "sudo"]
   }
   EOF
   ```

1. Create a JWT role `app` that validates tokens and assigns the appropriate policy.

   ```shell
   bao write auth/gitlab_rails_jwt/role/app \
      role_type=jwt \
      bound_audiences=http://gdk.test:8200 \
      user_claim=user_id \
      token_policies=secrets_manager
   ```

## Develop Secrets Manager features with OpenBao

1. Run openbao and unseal the vault.

   ```shell
   gdk start openbao && gdk bao configure
   ```

1. Enable the feature flag.

   ```shell
   Feature.enable(:secrets_manager)
   ```

1. In GitLab, on the left sidebar, select **Search or go to** and find your project.
1. Select **Settings > General**.
1. Expand **Visibility, project features, permissions**.
1. Turn on the **Secrets Manager** toggle, and wait for the Secrets Manager to be provisioned.
