---
title: Object storage
---

You can configure object storage in GDK.

Prerequisites:

- To use the GDK integration, you must install [MinIO](https://docs.minio.io/docs/minio-quickstart-guide) binary (no Docker image).
- To use the [MinIO console](https://github.com/minio/console), you must have at least [version `2021-07-08T01-15-01Z`](https://github.com/minio/minio/releases/tag/RELEASE.2021-07-08T01-15-01Z).
  If you use `mise` to manage MinIO, this dependency is managed for you.

You can enable the object store by adding the following to your `gdk.yml`:

```yaml
object_store:
  enabled: true
  port: 9000
```

The object store has the following default settings:

| Setting                | Default            | Description                                                                             |
|----------------------- |--------------------|-----------------------------------------------------------------------------------------|
| `enabled`              | `false`            | Enable or disable MinIO.                                                                |
| `port`                 | `9000`             | Port to bind MinIO.                                                                     |
| `console_port`         | `9002`             | Port to bind [MinIO Console](https://github.com/minio/console).                         |
| `access key`           | `minio`            | Access key needed by MinIO to log in via its web UI. Cannot be changed.                 |
| `secret key`           | `gdk-minio`        | Secret key needed by MinIO to log in via its web UI. Cannot be changed.                 |

Changing settings requires `gdk reconfigure` to be run.

## Object storage configuration

Available configuration keys for `object_store` are shown in the
[`gdk.example.yml`](https://gitlab.com/gitlab-org/gitlab-development-kit/-/blob/main/gdk.example.yml) file.

For each type of object storage (for example, `lfs`, `packages`, or `uploads`) you can also set three additional
keys that are available to all object storage types:

- `bucket`, to change the bucket name if required.
- `enabled` and `proxy_download`, to override the common setting for a specific object storage type.

You should set `consolidated_form` to `true`. For the full list of object storage settings, see the
[relevant documentation](https://docs.gitlab.com/ee/administration/object_storage.html).

The following sections provide a quick guide for configuring object storage for:

- External S3 providers.
- Google Cloud Storage.
- Microsoft Azure.

### External S3 providers

```yaml
object_store:
  enabled: true
  consolidated_form: true
  connection:
    provider: 'AWS'
    aws_access_key_id: '<YOUR AWS ACCESS KEY ID>'
    aws_secret_access_key: '<YOUR AWS SECRET ACCESS KEY>'
  objects:
    artifacts:
      bucket: artifacts
    backups:
      bucket: backups
    external_diffs:
      bucket: external-diffs
    lfs:
      bucket: lfs-objects
    uploads:
      bucket: uploads
    packages:
      bucket: packages
    dependency_proxy:
      bucket: dependency_proxy
    terraform_state:
      bucket: terraform
    pages:
      bucket: pages
    ci_secure_files:
      bucket: ci-secure-files
```

### Google Cloud Storage

```yaml
object_store:
  enabled: true
  consolidated_form: true
  connection:
    provider: 'Google'
    google_project: '<YOUR GOOGLE PROJECT ID>'
    google_json_key_location: '<YOUR PATH TO GCS CREDENTIALS>'
  objects:
    artifacts:
      bucket: artifacts
    backups:
      bucket: backups
    external_diffs:
      bucket: external-diffs
    lfs:
      bucket: lfs-objects
    uploads:
      bucket: uploads
    packages:
      bucket: packages
    dependency_proxy:
      bucket: dependency_proxy
    terraform_state:
      bucket: terraform
    pages:
      bucket: pages
    ci_secure_files:
      bucket: ci-secure-files
```

### Microsoft Azure Blob storage

To make Microsoft Azure Blob storage work, `consolidated_form` must be
set to `true`:

```yaml
object_store:
  enabled: true
  consolidated_form: true
  connection:
    provider: 'AzureRM'
    azure_storage_account_name: '<YOUR AZURE STORAGE ACCOUNT>'
    azure_storage_access_key: '<YOUR AZURE STORAGE ACCESS KEY>'
  objects:
    artifacts:
      bucket: artifacts
    backups:
      bucket: backups
    external_diffs:
      bucket: external-diffs
    lfs:
      bucket: lfs-objects
    uploads:
      bucket: uploads
    packages:
      bucket: packages
    dependency_proxy:
      bucket: dependency_proxy
    terraform_state:
      bucket: terraform
    pages:
      bucket: pages
    ci_secure_files:
      bucket: ci-secure-files
```

## Backups

To set the object storage config for backups, configure the bucket in `object_store.backup_remote_directory`, for example:

```yaml
object_store:
  enabled: false
  backup_remote_directory: 'backups'
```

## MinIO errors

If you cannot start MinIO, you may have an old version not supporting the `--compat` parameter.

`gdk tail minio` shows a crash loop with the following error:

```plaintext
Incorrect Usage: flag provided but not defined: -compat
```

Upgrading MinIO to the latest version fixes it.

## Creating a new bucket

In order to start using MinIO from your GitLab instance you have to create buckets first.
You can create a new bucket by accessing <http://127.0.0.1:9000/> (default configuration).
