---
title: Local network binding
---

- `gdk.test` is the standard hostname for referring to the local GDK instance.
- `registry.test` is the standard hostname for referring to a local [container registry](registry.md).

We recommend [mapping these to a loopback interface](#create-loopback-interface) because it's more flexible, but they can also be mapped to `127.0.0.1`.

## Local interface

To set up `gdk.test` and `registry.test` as hostnames using `127.0.0.1`:

1. Add the following to the end of `/etc/hosts` (you must use `sudo` to save the changes):

   ```plaintext
   127.0.0.1 gdk.test registry.test
   ```

1. Set `hostname` to `gdk.test`:

   ```shell
   gdk config set hostname gdk.test
   ```

1. Reconfigure GDK:

   ```shell
   gdk reconfigure
   ```

1. Restart GDK to use the new configuration:

   ```shell
   gdk restart
   ```

## Create loopback interface

Some functionality may not work if GDK processes listen on `localhost` or `127.0.0.1` (for example,
services [running under Docker](runner.md#executing-a-runner-from-within-docker)). Therefore, an IP address on a different private network should be
used.

`************` is a useful [private network address](https://en.wikipedia.org/wiki/Private_network#Private_IPv4_addresses)
that can avoid clashes with `localhost` and `127.0.0.1`.

To set up `gdk.test` and `registry.test` as hostnames using `************`:

1. Create an internal interface.

   For macOS, create an alias to the loopback adapter:

   ```shell
   sudo ifconfig lo0 alias ************
   ```

   For Linux, create a dummy interface:

   ```shell
   sudo ip link add dummy0 type dummy
   sudo ip address add ************ dev dummy0
   sudo ip link set dummy0 up
   ```

1. Add the following to the end of `/etc/hosts` (you must use `sudo` to save the changes):

   ```plaintext
   ************ gdk.test registry.test
   ```

Make sure you remove previous entries for `gdk.test`, otherwise the hosts file will respect the first entry.

1. Set `hostname` to `gdk.test`:

   ```shell
   gdk config set hostname gdk.test
   ```

1. Set `listen_address` to `************`:

   ```shell
   gdk config set listen_address ************
   ```

1. Reconfigure GDK:

   ```shell
   gdk reconfigure
   ```

1. Restart GDK to use the new configuration:

   ```shell
   gdk restart
   ```

1. Optional. Make the loopback alias [persist across reboots](#create-loopback-device-on-startup).

### Create loopback device on startup

For the loopback alias to work across reboots, the aliased IP address must be setup upon system boot.

#### macOS

To automate this on macOS, create a file called `org.gitlab1.ifconfig.plist` at `/Library/LaunchDaemons/` containing:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>org.gitlab1.ifconfig</string>
    <key>RunAtLoad</key>
    <true/>
    <key>Nice</key>
    <integer>10</integer>
    <key>ProgramArguments</key>
    <array>
      <string>/sbin/ifconfig</string>
      <string>lo0</string>
      <string>alias</string>
      <string>************</string>
    </array>
</dict>
</plist>
```

Ensure the created file has the correct permissions:

```shell
sudo chown root:wheel /Library/LaunchDaemons/org.gitlab1.ifconfig.plist
sudo chmod 644 /Library/LaunchDaemons/org.gitlab1.ifconfig.plist
```

#### Linux

The method to persist this dummy interface on Linux varies between distributions.

##### Ubuntu

On Ubuntu, you can run:

```shell
sudo nmcli connection add type dummy ifname dummy0 ip4 ************
```

## Access GDK on private IP address

You can access the GDK server through your computer's [private IP address](https://www.rfc-editor.org/rfc/rfc1918.html).
Use this method when you test on multiple devices on the same IP range or network.

> [!important]
> You must install the `gdk` instance in the default ` ~ ` location for these commands to work correctly.

### Find your local IP address (Mac or Linux)

From a terminal, run the following command:

```shell
# On macOS or Linux systems with net-tools installed
ifconfig -a

# On macOS using the default ethernet port or WiFi
# ethernet use `en1`. WiFi use `en0`.
ipconfig getifaddr en0

# On modern Linux systems where ifconfig is unavailable
ip addr
```

The command provides a list of all wired and wireless adapters that are available on your computer.
Look for the entry with `status: active` and note its `inet` address (like `*************`).
If you know your network's IP range, you can find your IP address by typing the following
command on your terminal:

```shell
# If your IP range is 192.168.xxx.xxx
ifconfig -a | grep 192.168
```

The command output displays your local IP address:

``` shell
inet ************* netmask 0xffffff00 broadcast *************
```

### Update configuration files

1. Update `gdk.yml`:

   ```yaml
   listen_address: *************  # Replace with your actual IP address
   ```

1. Reconfigure GDK:

   ```shell
   gdk reconfigure
   ```

1. Test for IP address connectivity:

   ```shell
   # Should print your IP address and port when server starts
   gdk start
   ```

### Map your local IP address to `gdk.test`

To set up `gdk.test` and `registry.test` as hostnames by using your local IP address:

1. Add the following to the end of `/etc/hosts` (you must use `sudo` to save the changes):

   ```plaintext
   ************* gdk.test registry.test  # Replace ************* with your actual IP address
   ```

1. Set `hostname` to `gdk.test`:

   ```shell
   gdk config set hostname gdk.test
   ```

1. Reconfigure GDK:

   ```shell
   gdk reconfigure
   ```

1. Test `gdk.test` connectivity:

   ```shell
   # Should print gdk.test and port when server starts
   gdk restart
   ```

### Troubleshooting

- If your router's dynamic host configuration protocol (DHCP) assigned your computer's IP address,
  you might need to update your configuration files if you change internet connections. This issue
  can happen if you switch from ethernet to a Wi-Fi connection. It can also happen if your router
  assigns a new IP address because your lease expired.
- If you try to load GDK by using the IP address from a Windows PC, check that your internet connection is
  [set to private](https://support.microsoft.com/en-us/windows/essential-network-settings-and-tasks-in-windows-f21a9bbc-c582-55cd-35e0-73431160a1b9).
  Only make this change if your PC is fully updated, and you are on a trusted network.
- If you can load your GDK instance by IP address but stylesheets, images, and scripts don't load,
  check the Network tab of your browser for `404` errors. You might need to update your local `/etc/hosts`
  file to match the GDK host's file configuration.
