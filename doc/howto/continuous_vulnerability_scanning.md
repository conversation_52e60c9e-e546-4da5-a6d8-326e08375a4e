---
title: Continuous Vulnerability Scanning
---

1. Add `export PM_SYNC_IN_DEV=true` to your [`env.runit` file](../contributing/runit.md#modify-service-configuration).
1. Choose package metadata to sync in the [admin area](https://docs.gitlab.com/ee/administration/settings/security_and_compliance.html#choose-package-registry-metadata-to-sync).
1. Package advisories are synchronized by a cron job, but you can execute the job immediately via the Rails console.

   ```shell
   ./bin/rails runner 'PackageMetadata::AdvisoriesSyncWorker.perform'
   ```
